import { DataTypes, Model, Sequelize } from 'sequelize';

export interface StudentStatusAttributes {
  id: string;
  student_id: string;
  is_online: boolean;
  last_heartbeat: Date;
  current_processes: string | null; // JSON字符串
  system_info: string | null; // JSON字符串
  created_at?: Date;
  updated_at?: Date;
}

export interface StudentStatusCreationAttributes extends Omit<StudentStatusAttributes, 'id' | 'created_at' | 'updated_at'> {}

export class StudentStatusInstance extends Model<StudentStatusAttributes, StudentStatusCreationAttributes> implements StudentStatusAttributes {
  public id!: string;
  public student_id!: string;
  public is_online!: boolean;
  public last_heartbeat!: Date;
  public current_processes!: string | null;
  public system_info!: string | null;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // 辅助方法：获取当前进程列表
  public getCurrentProcesses(): any[] {
    try {
      return this.current_processes ? JSON.parse(this.current_processes) : [];
    } catch {
      return [];
    }
  }

  // 辅助方法：设置当前进程列表
  public setCurrentProcesses(processes: any[]): void {
    this.current_processes = JSON.stringify(processes);
  }

  // 辅助方法：获取系统信息
  public getSystemInfo(): any {
    try {
      return this.system_info ? JSON.parse(this.system_info) : {};
    } catch {
      return {};
    }
  }

  // 辅助方法：设置系统信息
  public setSystemInfo(info: any): void {
    this.system_info = JSON.stringify(info);
  }
}

export const StudentStatus = (sequelize: Sequelize) => {
  StudentStatusInstance.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      student_id: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: 'students',
          key: 'id'
        },
        comment: '考生ID'
      },
      is_online: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否在线'
      },
      last_heartbeat: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '最后心跳时间'
      },
      current_processes: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: null,
        comment: '当前运行进程（JSON格式）'
      },
      system_info: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: null,
        comment: '系统信息（JSON格式）'
      }
    },
    {
      sequelize,
      modelName: 'StudentStatus',
      tableName: 'student_status',
      indexes: [
        {
          unique: true,
          fields: ['student_id']
        },
        {
          fields: ['is_online']
        },
        {
          fields: ['last_heartbeat']
        }
      ]
    }
  );

  return StudentStatusInstance;
};
