<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监考系统考生端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 100%;
            max-width: 800px;
            text-align: center;
        }

        /* 诚信考试承诺书样式 */
        .agreement-form {
            text-align: left;
            background: white;
            border: 2px solid #2c5aa0;
            border-radius: 8px;
            padding: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .agreement-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 20px;
            text-align: center;
            border-radius: 6px 6px 0 0;
        }

        .agreement-header h1 {
            margin: 0;
            font-size: 24px;
            color: #2c5aa0;
            font-weight: bold;
        }

        .agreement-content {
            padding: 25px;
            line-height: 1.6;
            color: #333;
        }

        .intro-text {
            margin-bottom: 20px;
            font-size: 14px;
            text-indent: 2em;
        }

        .agreement-items {
            margin-bottom: 25px;
        }

        .agreement-items p {
            margin-bottom: 15px;
            font-size: 14px;
            text-indent: 2em;
            line-height: 1.8;
        }

        .agreement-footer {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .checkbox-container {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            font-size: 13px;
            line-height: 1.5;
            color: #495057;
        }

        .checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 2px;
            transform: scale(1.2);
        }

        .agreement-buttons {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
            border-radius: 0 0 6px 6px;
        }

        .agree-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 15px;
            transition: background 0.3s;
        }

        .agree-btn:hover:not(:disabled) {
            background: #c82333;
        }

        .agree-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .reject-btn {
            background: #2c3e50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .reject-btn:hover {
            background: #1a252f;
        }

        /* 登录界面样式 */
        .login-form {
            display: none;
            text-align: center;
            padding: 40px;
            position: relative;
            z-index: 1;
        }

        .login-form h2 {
            color: #333;
            margin-bottom: 10px;
        }

        .login-form p {
            color: #666;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            color: #333;
            box-sizing: border-box;
            position: relative;
            z-index: 10;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .form-group input:disabled {
            background: #f5f5f5;
            cursor: not-allowed;
        }

        .login-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: #5a67d8;
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 监考界面样式 */
        .monitoring-form {
            display: none;
            text-align: left;
            padding: 20px;
            width: 100%;
            max-width: 1000px;
        }

        .monitoring-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }

        .monitoring-header h2 {
            color: #333;
            margin: 0;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .monitoring-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }

        .status-card h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-online {
            color: #28a745;
            font-weight: bold;
        }

        .status-offline {
            color: #dc3545;
            font-weight: bold;
        }

        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }

        .logs-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }

        .logs-section h4 {
            margin: 0 0 15px 0;
            color: #333;
        }

        .logs-container {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .log-time {
            color: #6c757d;
            margin-right: 8px;
        }

        .log-level-info {
            color: #17a2b8;
            margin-right: 8px;
        }

        .log-level-warning {
            color: #ffc107;
            margin-right: 8px;
        }

        .log-level-error {
            color: #dc3545;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 诚信考试承诺书 -->
        <div id="agreementForm" class="agreement-form">
            <div class="agreement-header">
                <h1 id="pledgeTitle">诚信考试承诺书</h1>
            </div>

            <div class="agreement-content">
                <div id="pledgeContent" class="agreement-items">
                    <!-- 承诺书内容将通过JavaScript动态加载 -->
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div>正在加载承诺书内容...</div>
                    </div>
                </div>

                <div class="agreement-footer" id="pledgeFooter" style="display: none;">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeCheckbox">
                        <span id="pledgeCheckboxText">本人已认真阅读以上诚信承诺规则，并保证严格遵守相关规定。</span>
                    </label>
                </div>
            </div>

            <div class="agreement-buttons" id="pledgeButtons" style="display: none;">
                <button id="agreeBtn" class="agree-btn" disabled>同意</button>
                <button id="rejectBtn" class="reject-btn">拒绝</button>
            </div>
        </div>

        <!-- 登录表单 -->
        <div id="loginForm" class="login-form">
            <h2>监考系统考生端</h2>
            <p>请输入您的登录信息</p>

            <div class="form-group">
                <label for="name">考生姓名</label>
                <input type="text" id="name" placeholder="请输入考生姓名" required>
            </div>

            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" placeholder="请输入手机号码" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="请输入密码" required>
            </div>

            <button id="loginBtn" class="login-btn">登录</button>

            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666; text-align: left;">
                <strong>测试账号（离线模式）：</strong><br>
                • 张三 - 13800138001 - 123456<br>
                • 李四 - 13800138002 - 123456<br>
                • 王五 - 13800138003 - 123456<br>
                <br>
                <strong>登录说明：</strong><br>
                • 系统会自动检测服务器连接状态<br>
                • 服务器可用时使用在线验证<br>
                • 服务器不可用时自动切换到离线模式<br>
                • 登录成功后将自动启动监考服务<br>
                <br>
                <strong>服务器地址：</strong>localhost:3000
            </div>
        </div>

        <!-- 监考主界面 -->
        <div id="monitoringForm" class="monitoring-form" style="display: none;">
            <div class="monitoring-header">
                <h2>监考系统运行中</h2>
                <div class="student-info">
                    <span id="studentDisplay">考生：未登录</span>
                    <button id="logoutBtn" class="logout-btn">退出登录</button>
                </div>
            </div>

            <div class="monitoring-content">
                <div class="status-grid">
                    <div class="status-card">
                        <h4>🔗 连接状态</h4>
                        <div class="status-item">
                            <span>服务器连接：</span>
                            <span id="serverStatus" class="status-online">已连接</span>
                        </div>
                        <div class="status-item">
                            <span>最后心跳：</span>
                            <span id="lastHeartbeat">--:--:--</span>
                        </div>
                    </div>

                    <div class="status-card">
                        <h4>📊 监控状态</h4>
                        <div class="status-item">
                            <span>运行时间：</span>
                            <span id="runningTime">00:00:00</span>
                        </div>
                        <div class="status-item">
                            <span>违规次数：</span>
                            <span id="violationCount">0</span>
                        </div>
                    </div>

                    <div class="status-card">
                        <h4>🖥️ 系统信息</h4>
                        <div class="status-item">
                            <span>硬盘序列号：</span>
                            <span id="diskSerial">获取中...</span>
                        </div>
                        <div class="status-item">
                            <span>IP地址：</span>
                            <span id="ipAddress">获取中...</span>
                        </div>
                    </div>
                </div>

                <div class="logs-section">
                    <h4>📝 系统日志</h4>
                    <div class="logs-container" id="systemLogs">
                        <div class="log-entry">
                            <span class="log-time">--:--:--</span>
                            <span class="log-level-info">[INFO]</span>
                            系统启动中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const agreementForm = document.getElementById('agreementForm');
        const loginForm = document.getElementById('loginForm');
        const monitoringForm = document.getElementById('monitoringForm');
        const agreeCheckbox = document.getElementById('agreeCheckbox');
        const agreeBtn = document.getElementById('agreeBtn');
        const rejectBtn = document.getElementById('rejectBtn');
        const nameInput = document.getElementById('name');
        const phoneInput = document.getElementById('phone');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');

        // 监考界面元素
        const studentDisplay = document.getElementById('studentDisplay');
        const serverStatus = document.getElementById('serverStatus');
        const lastHeartbeat = document.getElementById('lastHeartbeat');
        const runningTime = document.getElementById('runningTime');
        const violationCount = document.getElementById('violationCount');
        const diskSerial = document.getElementById('diskSerial');
        const ipAddress = document.getElementById('ipAddress');
        const systemLogs = document.getElementById('systemLogs');

        // 承诺书相关函数
        function showAgreementForm() {
            agreementForm.style.display = 'block';
            loginForm.style.display = 'none';
            // 加载承诺书配置
            loadPledgeConfig();
        }

        // 加载承诺书配置
        async function loadPledgeConfig() {
            try {
                console.log('正在加载承诺书配置...');

                const response = await fetch(`${API_BASE_URL}/config/pledge`);
                const result = await response.json();

                if (result.success && result.data) {
                    const pledgeConfig = result.data;
                    console.log('承诺书配置加载成功:', pledgeConfig);

                    // 检查是否启用承诺书
                    if (!pledgeConfig.enabled) {
                        console.log('承诺书已禁用，直接跳转到登录');
                        showLoginForm();
                        return;
                    }

                    // 更新承诺书标题
                    document.getElementById('pledgeTitle').textContent = pledgeConfig.title || '诚信考试承诺书';

                    // 更新承诺书内容
                    const pledgeContent = document.getElementById('pledgeContent');
                    pledgeContent.innerHTML = `<div style="white-space: pre-line; line-height: 1.6; text-align: left; padding: 20px;">${pledgeConfig.content || ''}</div>`;

                    // 根据配置决定是否显示签名确认
                    const pledgeFooter = document.getElementById('pledgeFooter');
                    const pledgeButtons = document.getElementById('pledgeButtons');

                    if (pledgeConfig.requireSignature) {
                        // 需要签名确认
                        pledgeFooter.style.display = 'block';
                        pledgeButtons.style.display = 'block';

                        // 更新确认文本
                        const checkboxText = document.getElementById('pledgeCheckboxText');
                        checkboxText.textContent = '本人已认真阅读以上诚信承诺规则，并保证严格遵守相关规定。如有违反，自愿承担相应责任和后果。';
                    } else {
                        // 不需要签名，只显示内容和继续按钮
                        pledgeFooter.style.display = 'none';
                        pledgeButtons.innerHTML = '<button id="continueBtn" class="agree-btn">继续</button>';
                        pledgeButtons.style.display = 'block';

                        // 绑定继续按钮事件
                        document.getElementById('continueBtn').addEventListener('click', () => {
                            console.log('用户已阅读承诺书内容');
                            showLoginForm();
                        });
                    }

                    addSystemLog('info', '承诺书配置加载完成');
                } else {
                    console.error('加载承诺书配置失败:', result.message);
                    // 使用默认承诺书内容
                    loadDefaultPledge();
                }
            } catch (error) {
                console.error('加载承诺书配置异常:', error);
                // 使用默认承诺书内容
                loadDefaultPledge();
            }
        }

        // 加载默认承诺书内容
        function loadDefaultPledge() {
            console.log('使用默认承诺书内容');

            const defaultContent = `我是参加"2025年全省电子物证技能力测评"的考生，愿意在考试中自觉遵守各项考试规定，现作重要承诺：

一、同意考试组委会对本人考试全程进行监考，考试所用设备均主动按照组委会要求安装远程监考软件，主动配合组委会实现监考巡考。

二、严格遵守能力考核考试规则，保证按规定的程序和要求参加考试，如有违反，自愿承担作弊后果。

三、服从组委会的管理，自觉遵守考试纪律。

四、不使用除考试设备外的通讯工具。

五、考试时独立思考，不抄袭或有意让他人抄袭答案。`;

            // 更新内容
            document.getElementById('pledgeTitle').textContent = '诚信考试承诺书';
            document.getElementById('pledgeContent').innerHTML = `<div style="white-space: pre-line; line-height: 1.6; text-align: left; padding: 20px;">${defaultContent}</div>`;

            // 显示签名确认
            document.getElementById('pledgeFooter').style.display = 'block';
            document.getElementById('pledgeButtons').style.display = 'block';

            addSystemLog('warning', '使用默认承诺书内容');
        }

        // 显示违规通知
        async function showViolationNotification(violationType, processName) {
            try {
                // 只显示温和的右下角通知
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    await ipcRenderer.invoke('show-violation-notification', violationType, processName);
                }

                // 在页面中显示温和的提示
                showViolationToast(processName);
            } catch (error) {
                console.error('显示违规通知失败:', error);
            }
        }

        // 显示温和的违规提示（右下角小提示）
        function showViolationToast(processName) {
            // 创建温和的提示框
            const toastHtml = `
                <div id="violationToast" style="
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: #ff9800;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 1000;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    max-width: 300px;
                    animation: slideIn 0.3s ease-out;
                ">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 16px; margin-right: 8px;">⚠️</span>
                        <strong>监考提醒</strong>
                    </div>
                    <div style="font-size: 13px; line-height: 1.4;">
                        检测到违规程序: <strong>${processName}</strong><br>
                        系统已自动关闭，请注意遵守考试规定
                    </div>
                </div>
                <style>
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                </style>
            `;

            // 移除已存在的提示
            const existingToast = document.getElementById('violationToast');
            if (existingToast) {
                existingToast.remove();
            }

            // 添加新的提示
            document.body.insertAdjacentHTML('beforeend', toastHtml);

            // 5秒后自动消失
            setTimeout(() => {
                const toast = document.getElementById('violationToast');
                if (toast) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        toast.remove();
                    }, 300);
                }
            }, 5000);
        }

        // 显示普通通知
        async function showNotification(title, body, isUrgent = false) {
            try {
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    await ipcRenderer.invoke('show-notification', title, body, isUrgent);
                }
            } catch (error) {
                console.error('显示通知失败:', error);
            }
        }

        function showLoginForm() {
            agreementForm.style.display = 'none';
            loginForm.style.display = 'block';
            monitoringForm.style.display = 'none';

            // 调试：检查输入框状态
            console.log('登录表单已显示');
            console.log('姓名输入框:', nameInput);
            console.log('手机号输入框:', phoneInput);
            console.log('密码输入框:', passwordInput);

            // 确保输入框可用
            setTimeout(() => {
                nameInput.disabled = false;
                phoneInput.disabled = false;
                passwordInput.disabled = false;
                nameInput.focus();
                console.log('输入框已启用，焦点已设置');
            }, 100);
        }

        function showMonitoringForm(studentInfo) {
            agreementForm.style.display = 'none';
            loginForm.style.display = 'none';
            monitoringForm.style.display = 'block';

            // 显示考生信息
            studentDisplay.textContent = `考生：${studentInfo.name} (${studentInfo.policeId})`;

            // 显示系统信息
            diskSerial.textContent = studentInfo.diskSerialNumber || '获取中...';

            // 添加登录成功日志
            addSystemLog('info', `考生 ${studentInfo.name} 登录成功`);
            addSystemLog('info', '监考服务已启动');

            console.log('监考界面已显示');
        }

        function handleAgreeCheckbox() {
            agreeBtn.disabled = !agreeCheckbox.checked;
        }

        function handleAgree() {
            if (!agreeCheckbox.checked) {
                alert('请先勾选承诺书确认选项');
                return;
            }

            console.log('用户已同意诚信考试承诺书');
            showLoginForm();
        }

        function handleReject() {
            const confirmed = confirm('拒绝承诺书将无法参加考试，确定要退出吗？');
            if (confirmed) {
                console.log('用户拒绝诚信考试承诺书，程序退出');
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    ipcRenderer.invoke('quit-app');
                } else {
                    window.close();
                }
            }
        }

        function handleLogin() {
            const name = nameInput.value.trim();
            const phone = phoneInput.value.trim();
            const password = passwordInput.value.trim();

            // 验证必填字段
            if (!name || !phone || !password) {
                alert('请填写完整的登录信息（姓名、手机号、密码）');
                return;
            }

            // 验证姓名格式（2-4个中文字符）
            if (!/^[\u4e00-\u9fa5]{2,4}$/.test(name)) {
                alert('请输入正确的姓名格式（2-4个中文字符）');
                nameInput.focus();
                return;
            }

            // 验证手机号格式
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                alert('请输入正确的手机号格式');
                phoneInput.focus();
                return;
            }

            // 验证密码长度
            if (password.length < 6) {
                alert('密码长度不能少于6位');
                passwordInput.focus();
                return;
            }

            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            // 真实的身份验证过程
            setTimeout(async () => {
                // 验证用户身份
                const authResult = await authenticateUser(name, phone, password);

                if (authResult.success) {
                    // 登录成功
                    const studentInfo = authResult.studentInfo;

                    // 保存学生信息和token
                    localStorage.setItem('studentInfo', JSON.stringify(studentInfo));
                    localStorage.setItem('authToken', studentInfo.token);

                    console.log('登录成功，准备进入监考界面', studentInfo);

                    // 切换到监考界面
                    showMonitoringForm(studentInfo);

                    // 启动监控服务
                    startMonitoringServices(studentInfo);

                    // 通知主进程更新登录状态（登录成功后会自动隐藏到托盘）
                    if (window.require) {
                        const { ipcRenderer } = window.require('electron');
                        ipcRenderer.invoke('update-login-status', true);
                    }

                    // 清空登录表单
                    nameInput.value = '';
                    phoneInput.value = '';
                    passwordInput.value = '';

                } else {
                    // 登录失败
                    alert(`登录失败！\n\n错误信息：${authResult.message}`);
                    console.log('登录失败:', authResult.message);
                }

                loginBtn.disabled = false;
                loginBtn.textContent = '登录';

            }, 2000);
        }

        // API配置
        const API_BASE_URL = 'http://localhost:3000/api';

        // 获取硬盘序列号
        async function getDiskSerialNumber() {
            try {
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    const systemInfo = await ipcRenderer.invoke('get-system-info');
                    return systemInfo.diskSerialNumber || 'UNKNOWN_DISK';
                } else {
                    // 浏览器环境下的模拟
                    return 'BROWSER_DISK_' + Date.now().toString().slice(-8);
                }
            } catch (error) {
                console.error('获取硬盘序列号失败:', error);
                return 'ERROR_DISK_' + Date.now().toString().slice(-8);
            }
        }

        // 身份验证函数 - 与后台API对接
        async function authenticateUser(name, phone, password) {
            try {
                // 获取硬盘序列号
                const diskSerialNumber = await getDiskSerialNumber();
                console.log('硬盘序列号:', diskSerialNumber);

                // 首先检查服务器是否可用
                const serverAvailable = await checkServerAvailability();

                if (serverAvailable) {
                    // 调用后台登录API
                    console.log('发送登录请求:', {
                        url: `${API_BASE_URL}/auth/login`,
                        phone: phone,
                        password: password,
                        diskSerialNumber: diskSerialNumber
                    });

                    const response = await fetch(`${API_BASE_URL}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            phone: phone,
                            password: password,
                            diskSerialNumber: diskSerialNumber
                        })
                    });

                    const result = await response.json();
                    console.log('登录API响应:', result);

                    if (response.ok && result.success) {
                        // 登录成功
                        return {
                            success: true,
                            studentInfo: {
                                id: result.data.student.id,
                                name: result.data.student.name,
                                phone: result.data.student.phone,
                                policeId: result.data.student.policeId,
                                department: result.data.student.department,
                                major: result.data.student.major,
                                token: result.data.token,
                                diskSerialNumber: diskSerialNumber,
                                loginTime: new Date().toISOString()
                            }
                        };
                    } else {
                        // 登录失败
                        return {
                            success: false,
                            message: result.message || `HTTP ${response.status}: ${response.statusText}`
                        };
                    }
                } else {
                    // 服务器不可用，使用离线模式
                    console.log('服务器不可用，尝试离线验证');
                    return await offlineAuthenticate(name, phone, password, diskSerialNumber);
                }
            } catch (error) {
                console.error('登录请求失败:', error);

                // 网络错误，尝试离线验证
                const diskSerialNumber = await getDiskSerialNumber();
                return await offlineAuthenticate(name, phone, password, diskSerialNumber);
            }
        }

        // 检查服务器可用性
        async function checkServerAvailability() {
            try {
                const response = await fetch(`http://localhost:3000/health`, {
                    method: 'GET'
                });
                console.log('健康检查响应:', response.status, response.ok);
                return response.ok;
            } catch (error) {
                console.log('服务器健康检查失败:', error);
                return false;
            }
        }

        // 离线验证（测试模式）
        async function offlineAuthenticate(name, phone, password, diskSerialNumber) {
            console.log('使用离线验证模式');

            // 预设的测试账号
            const testUsers = [
                {
                    name: '张三',
                    phone: '13800138001',
                    password: '123456',
                    policeId: 'P001',
                    department: '技术侦查支队',
                    major: '声纹识别'
                },
                {
                    name: '李四',
                    phone: '13800138002',
                    password: '123456',
                    policeId: 'P002',
                    department: '刑事科学技术支队',
                    major: '图像处理'
                },
                {
                    name: '王五',
                    phone: '13800138003',
                    password: '123456',
                    policeId: 'P003',
                    department: '网络安全保卫支队',
                    major: '电子物证'
                }
            ];

            // 查找匹配的用户
            const user = testUsers.find(u =>
                u.name === name &&
                u.phone === phone &&
                u.password === password
            );

            if (user) {
                return {
                    success: true,
                    studentInfo: {
                        id: user.policeId,
                        name: user.name,
                        phone: user.phone,
                        policeId: user.policeId,
                        department: user.department,
                        major: user.major,
                        token: 'offline_token_' + Date.now(),
                        diskSerialNumber: diskSerialNumber,
                        loginTime: new Date().toISOString(),
                        isOfflineMode: true
                    }
                };
            } else {
                return {
                    success: false,
                    message: '离线模式：用户名、手机号或密码错误'
                };
            }
        }

        // 退出登录
        function handleLogout() {
            const confirmed = confirm('确定要退出登录吗？退出后将停止监考服务。');
            if (confirmed) {
                // 停止监控服务
                stopMonitoringServices();

                // 清除本地存储
                localStorage.removeItem('studentInfo');
                localStorage.removeItem('authToken');

                // 重置界面
                showLoginForm();

                // 重置状态
                currentStudentInfo = null;

                console.log('用户已退出登录');
                addSystemLog('info', '用户已退出登录');
            }
        }

        // 添加系统日志
        function addSystemLog(level, message) {
            const timestamp = new Date().toLocaleTimeString('zh-CN');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-time">${timestamp}</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;

            // 添加到日志容器顶部
            if (systemLogs) {
                systemLogs.insertBefore(logEntry, systemLogs.firstChild);

                // 限制日志条数，保留最新的50条
                const logEntries = systemLogs.querySelectorAll('.log-entry');
                if (logEntries.length > 50) {
                    systemLogs.removeChild(logEntries[logEntries.length - 1]);
                }
            }

            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // 更新运行时间
        let startTime = null;
        function updateRunningTime() {
            if (startTime && runningTime) {
                const elapsed = Date.now() - startTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);

                const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                runningTime.textContent = timeStr;
            }
        }

        // 监控服务
        let heartbeatInterval = null;
        let statusReportInterval = null;
        let currentStudentInfo = null;

        // 启动监控服务
        function startMonitoringServices(studentInfo) {
            currentStudentInfo = studentInfo;
            startTime = Date.now();
            console.log('启动监控服务...');

            // 启动心跳
            startHeartbeat();

            // 启动状态上报
            startStatusReporting();

            // 启动进程监控
            startProcessMonitoring();

            // 启动界面更新
            startUIUpdates();

            // 启动USB控制检查
            startUSBControl();

            addSystemLog('info', '所有监控服务已启动');
        }

        // 启动界面更新
        function startUIUpdates() {
            // 每秒更新运行时间
            setInterval(updateRunningTime, 1000);

            // 立即更新系统信息
            updateSystemInfo();
        }

        // 更新系统信息显示
        async function updateSystemInfo() {
            try {
                const systemInfo = await getSystemInfo();
                if (diskSerial) {
                    diskSerial.textContent = systemInfo.diskSerialNumber || '未知';
                }
                if (ipAddress) {
                    ipAddress.textContent = systemInfo.ipAddress || '未知';
                }
            } catch (error) {
                console.error('更新系统信息失败:', error);
            }
        }

        // 停止监控服务
        function stopMonitoringServices() {
            console.log('停止监控服务...');

            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }

            if (statusReportInterval) {
                clearInterval(statusReportInterval);
                statusReportInterval = null;
            }
        }

        // 心跳服务
        function startHeartbeat() {
            // 立即发送一次心跳
            sendHeartbeat();

            // 每30秒发送心跳
            heartbeatInterval = setInterval(sendHeartbeat, 30000);
        }

        async function sendHeartbeat() {
            try {
                const token = localStorage.getItem('authToken');
                const studentInfo = JSON.parse(localStorage.getItem('studentInfo') || '{}');
                const studentId = studentInfo.policeId || 'UNKNOWN';

                if (!token) return;

                const response = await fetch(`${API_BASE_URL}/monitoring/heartbeat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        studentId: studentId,
                        systemInfo: {},
                        processes: [],
                        timestamp: new Date().toISOString()
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log('心跳发送成功');
                    // 更新界面状态
                    if (serverStatus) {
                        serverStatus.textContent = '已连接';
                        serverStatus.className = 'status-online';
                    }
                    if (lastHeartbeat) {
                        lastHeartbeat.textContent = new Date().toLocaleTimeString('zh-CN');
                    }
                } else {
                    console.error('心跳发送失败:', result.message);
                    if (serverStatus) {
                        serverStatus.textContent = '连接异常';
                        serverStatus.className = 'status-warning';
                    }
                    addSystemLog('warning', '心跳发送失败: ' + result.message);
                }
            } catch (error) {
                console.error('心跳发送错误:', error);
                if (serverStatus) {
                    serverStatus.textContent = '连接断开';
                    serverStatus.className = 'status-offline';
                }
                addSystemLog('error', '心跳发送错误: ' + error.message);
            }
        }

        // 状态上报服务
        function startStatusReporting() {
            // 立即上报一次状态
            reportSystemStatus();

            // 每60秒上报状态
            statusReportInterval = setInterval(reportSystemStatus, 60000);
        }

        async function reportSystemStatus() {
            try {
                const token = localStorage.getItem('authToken');
                const studentInfo = JSON.parse(localStorage.getItem('studentInfo') || '{}');
                const studentId = studentInfo.policeId || 'UNKNOWN';

                if (!token) return;

                // 获取系统信息
                const systemInfo = await getSystemInfo();
                const processes = await getCurrentProcesses();

                const response = await fetch(`${API_BASE_URL}/monitoring/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        studentId: studentId,
                        systemInfo: systemInfo,
                        processes: processes,
                        violations: [],
                        timestamp: new Date().toISOString()
                    })
                });

                const result = await response.json();
                if (result.success) {
                    console.log('状态上报成功');
                } else {
                    console.error('状态上报失败:', result.message);
                }
            } catch (error) {
                console.error('状态上报错误:', error);
            }
        }

        // 获取系统信息
        async function getSystemInfo() {
            try {
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    return await ipcRenderer.invoke('get-system-info');
                } else {
                    // 浏览器环境模拟
                    return {
                        osVersion: navigator.platform,
                        computerName: 'Browser-PC',
                        ipAddress: '127.0.0.1',
                        macAddress: '00:00:00:00:00:00',
                        diskSerialNumber: await getDiskSerialNumber(),
                        totalMemory: navigator.deviceMemory ? navigator.deviceMemory * 1024 * 1024 * 1024 : 8589934592,
                        freeMemory: 4294967296
                    };
                }
            } catch (error) {
                console.error('获取系统信息失败:', error);
                return {};
            }
        }

        // 获取当前进程
        async function getCurrentProcesses() {
            try {
                if (window.require) {
                    const { ipcRenderer } = window.require('electron');
                    return await ipcRenderer.invoke('get-running-processes');
                } else {
                    // 浏览器环境模拟
                    return [];
                }
            } catch (error) {
                console.error('获取进程信息失败:', error);
                return [];
            }
        }

        // 违规记录缓存，避免重复上报
        const violationCache = new Map();
        const usbViolationCache = new Map();
        const VIOLATION_CACHE_DURATION = 1 * 60 * 1000; // 1分钟缓存时间

        // USB控制状态缓存
        let lastUSBControlAttempt = 0;
        let lastUSBControlResult = null;
        const USB_CONTROL_RETRY_INTERVAL = 10 * 60 * 1000; // 10分钟重试间隔

        // 进程监控
        function startProcessMonitoring() {
            // 每10秒检查一次进程
            setInterval(checkBlacklistedProcesses, 10000);

            // 每分钟清理过期的违规缓存
            setInterval(cleanViolationCache, 60000);
        }

        // 清理过期的违规缓存
        function cleanViolationCache() {
            const now = Date.now();

            // 清理进程违规缓存
            for (const [key, timestamp] of violationCache.entries()) {
                if (now - timestamp > VIOLATION_CACHE_DURATION) {
                    violationCache.delete(key);
                }
            }

            // 清理USB违规缓存
            for (const [key, timestamp] of usbViolationCache.entries()) {
                if (now - timestamp > VIOLATION_CACHE_DURATION) {
                    usbViolationCache.delete(key);
                }
            }
        }

        // 生成违规记录的唯一标识（只基于进程名，不包含PID）
        function getViolationKey(process) {
            return process.name.toLowerCase();
        }

        // 检查是否为新的违规记录
        function isNewViolation(process) {
            const key = getViolationKey(process);
            const now = Date.now();
            const lastReported = violationCache.get(key);

            if (!lastReported) {
                console.log(`🆕 新违规进程: ${process.name} (PID: ${process.pid})`);
                violationCache.set(key, now);
                return true;
            } else if ((now - lastReported) > VIOLATION_CACHE_DURATION) {
                console.log(`⏰ 违规进程缓存过期: ${process.name} (PID: ${process.pid}), 距离上次: ${Math.round((now - lastReported) / 1000)}秒`);
                violationCache.set(key, now);
                return true;
            } else {
                console.log(`⏭️ 跳过重复违规: ${process.name} (PID: ${process.pid}), 距离上次: ${Math.round((now - lastReported) / 1000)}秒`);
                return false;
            }
        }

        // 生成USB违规记录的唯一标识
        function getUSBViolationKey(device) {
            return `usb_${device.drive}`;
        }

        // 检查是否为新的USB违规记录
        function isNewUSBViolation(device) {
            const key = getUSBViolationKey(device);
            const now = Date.now();
            const lastReported = usbViolationCache.get(key);

            if (!lastReported || (now - lastReported) > VIOLATION_CACHE_DURATION) {
                usbViolationCache.set(key, now);
                return true;
            }
            return false;
        }

        async function checkBlacklistedProcesses() {
            try {
                const token = localStorage.getItem('authToken');
                if (!token) return;

                const processes = await getCurrentProcesses();

                // 获取黑名单
                const blacklistResponse = await fetch(`${API_BASE_URL}/config/blacklist`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (blacklistResponse.ok) {
                    const blacklistResult = await blacklistResponse.json();
                    console.log('黑名单数据:', blacklistResult);

                    // 正确获取黑名单数组
                    const blacklist = blacklistResult.data?.blacklistedProcesses || blacklistResult.data || [];
                    console.log('黑名单进程:', blacklist);

                    // 确保blacklist是数组
                    if (!Array.isArray(blacklist)) {
                        console.error('黑名单数据格式错误:', blacklist);
                        return;
                    }

                    // 检查违规进程
                    const violations = processes.filter(process => {
                        // 跳过系统关键进程
                        const systemProcesses = ['system', 'registry', 'smss.exe', 'csrss.exe', 'wininit.exe', 'winlogon.exe'];
                        if (systemProcesses.some(sys => process.name.toLowerCase().includes(sys))) {
                            return false;
                        }

                        return blacklist.some(blackItem =>
                            process.name.toLowerCase().includes(blackItem.toLowerCase())
                        );
                    });

                    if (violations.length > 0) {
                        console.log(`🚨 检测到 ${violations.length} 个违规进程:`, violations.map(v => v.name));

                        // 过滤出新的违规记录（避免重复上报）
                        const newViolations = violations.filter(process => isNewViolation(process));

                        if (newViolations.length > 0) {
                            console.log(`📝 新违规记录 ${newViolations.length} 个:`, newViolations.map(v => `${v.name}(${v.pid})`));

                            // 显示违规通知
                            for (const violation of newViolations) {
                                showViolationNotification('违规进程', violation.name);
                                addSystemLog('warning', `检测到违规进程: ${violation.name} (PID: ${violation.pid})`);
                            }

                            // 上报新的违规记录
                            await reportViolations(newViolations);
                        } else {
                            console.log('⏭️ 所有违规进程都已在缓存中，跳过上报');
                        }

                        // 立即结束所有违规进程（无论是否新违规）
                        await terminateViolationProcesses(violations);
                    }
                }
            } catch (error) {
                console.error('进程监控错误:', error);
            }
        }

        // 结束违规进程
        async function terminateViolationProcesses(violations) {
            const terminatedProcesses = [];
            const failedProcesses = [];

            for (const violation of violations) {
                try {
                    console.log(`🔪 正在结束违规进程: ${violation.name} (PID: ${violation.pid})`);

                    // 调用主进程结束进程
                    const result = await require('electron').ipcRenderer.invoke('terminate-process', violation.pid);

                    if (result.success) {
                        terminatedProcesses.push(violation);
                        console.log(`✅ 成功结束进程: ${violation.name} (PID: ${violation.pid})`);

                        // 显示警告弹窗
                        showViolationWarning(violation);
                    } else {
                        failedProcesses.push(violation);
                        console.log(`⚠️ 结束进程失败: ${violation.name} (PID: ${violation.pid}) - 进程可能已结束`);
                    }
                } catch (error) {
                    failedProcesses.push(violation);
                    console.log(`⚠️ 结束进程异常: ${violation.name} (PID: ${violation.pid}) - 进程可能已结束`);
                }
            }

            // 更新违规统计
            if (terminatedProcesses.length > 0) {
                updateViolationCount(terminatedProcesses.length);
            }

            console.log(`📊 进程结束结果: 成功 ${terminatedProcesses.length} 个, 失败 ${failedProcesses.length} 个`);

            return {
                terminated: terminatedProcesses,
                failed: failedProcesses
            };
        }

        // 显示违规警告弹窗
        function showViolationWarning(violation) {
            // 创建警告弹窗
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4d4f;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;

            warningDiv.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 5px;">🚨 违规进程已结束</div>
                <div>进程名称: ${violation.name}</div>
                <div>进程ID: ${violation.pid}</div>
                <div style="margin-top: 8px; font-size: 12px; opacity: 0.9;">
                    请勿运行违规软件，继续违规可能导致考试终止！
                </div>
            `;

            document.body.appendChild(warningDiv);

            // 5秒后自动移除
            setTimeout(() => {
                if (warningDiv.parentNode) {
                    warningDiv.parentNode.removeChild(warningDiv);
                }
            }, 5000);
        }

        // 更新违规次数显示
        function updateViolationCount(newViolations) {
            const violationElement = document.getElementById('violation-count');
            if (violationElement) {
                const currentCount = parseInt(violationElement.textContent) || 0;
                const newCount = currentCount + newViolations;
                violationElement.textContent = newCount;

                // 如果违规次数过多，显示严重警告
                if (newCount >= 3) {
                    showSevereWarning(newCount);
                }
            }
        }

        // 显示严重警告
        function showSevereWarning(violationCount) {
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #ff1744;
                color: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0,0,0,0.5);
                z-index: 20000;
                text-align: center;
                font-size: 16px;
                max-width: 400px;
            `;

            warningDiv.innerHTML = `
                <div style="font-size: 24px; margin-bottom: 15px;">⚠️ 严重警告 ⚠️</div>
                <div style="font-weight: bold; margin-bottom: 10px;">
                    您已违规 ${violationCount} 次！
                </div>
                <div style="margin-bottom: 15px;">
                    继续违规将导致考试强制结束！<br>
                    请立即停止使用违规软件！
                </div>
                <button onclick="this.parentNode.parentNode.removeChild(this.parentNode)"
                        style="background: white; color: #ff1744; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;">
                    我知道了
                </button>
            `;

            document.body.appendChild(warningDiv);
        }

        // 上报违规
        async function reportViolations(violations) {
            try {
                const token = localStorage.getItem('authToken');
                const studentInfo = JSON.parse(localStorage.getItem('studentInfo') || '{}');
                const studentId = studentInfo.policeId || 'UNKNOWN';

                if (!token) return;

                // 批量上报违规
                await fetch(`${API_BASE_URL}/monitoring/violation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        studentId: studentId,
                        violations: violations.map(violation => ({
                            type: 'blacklisted_process',
                            severity: 'high',
                            description: `检测到违规进程: ${violation.name}，已自动结束`,
                            details: {
                                ...violation,
                                action: 'terminated',
                                terminatedAt: new Date().toISOString()
                            },
                            name: violation.name,
                            pid: violation.pid,
                            action: 'terminated'
                        })),
                        timestamp: new Date().toISOString()
                    })
                });

                console.log('违规上报完成:', violations);
            } catch (error) {
                console.error('违规上报错误:', error);
            }
        }

        // 根据姓名生成模拟的考生信息（备用函数，现在不使用）
        function generateStudentInfo(name, phone) {
            const departments = [
                '技术侦查支队',
                '刑事科学技术支队',
                '网络安全保卫支队',
                '经济犯罪侦查支队',
                '法医鉴定中心',
                '交通管理支队'
            ];

            const majors = [
                '声纹识别',
                '图像处理',
                '电子物证',
                '痕迹检验',
                'DNA检验',
                '网络安全',
                '交通事故处理',
                '金融犯罪侦查'
            ];

            // 根据姓名生成一个相对固定的索引
            let nameHash = 0;
            for (let i = 0; i < name.length; i++) {
                nameHash += name.charCodeAt(i);
            }

            const deptIndex = nameHash % departments.length;
            const majorIndex = nameHash % majors.length;
            const policeIdNum = String(nameHash % 9999 + 1).padStart(4, '0');

            return {
                name: name,
                phone: phone,
                policeId: `P${policeIdNum}`,
                department: departments[deptIndex],
                major: majors[majorIndex],
                diskSerialNumber: `SN${Date.now().toString().slice(-8)}`,
                loginTime: new Date().toISOString()
            };
        }

        // 事件监听
        agreeCheckbox.addEventListener('change', handleAgreeCheckbox);
        agreeBtn.addEventListener('click', handleAgree);
        rejectBtn.addEventListener('click', handleReject);
        loginBtn.addEventListener('click', handleLogin);
        logoutBtn.addEventListener('click', handleLogout);

        // 调试输入框事件
        nameInput.addEventListener('focus', () => console.log('姓名输入框获得焦点'));
        nameInput.addEventListener('input', (e) => console.log('姓名输入:', e.target.value));
        phoneInput.addEventListener('focus', () => console.log('手机号输入框获得焦点'));
        phoneInput.addEventListener('input', (e) => console.log('手机号输入:', e.target.value));
        passwordInput.addEventListener('focus', () => console.log('密码输入框获得焦点'));
        passwordInput.addEventListener('input', (e) => console.log('密码输入:', e.target.value));

        // 支持回车键登录
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                phoneInput.focus();
            }
        });

        phoneInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                passwordInput.focus();
            }
        });

        passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });

        // 页面关闭时清理
        window.addEventListener('beforeunload', function() {
            stopMonitoringServices();
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('监考系统考生端已加载');
            showAgreementForm();

            // 检查是否有保存的登录信息
            const savedStudentInfo = localStorage.getItem('studentInfo');
            const savedToken = localStorage.getItem('authToken');

            if (savedStudentInfo && savedToken) {
                console.log('发现保存的登录信息，尝试自动登录...');
                try {
                    const studentInfo = JSON.parse(savedStudentInfo);
                    // 验证token是否仍然有效
                    verifyToken(savedToken).then(isValid => {
                        if (isValid) {
                            console.log('自动登录成功');
                            startMonitoringServices(studentInfo);
                        } else {
                            console.log('Token已过期，需要重新登录');
                            localStorage.removeItem('studentInfo');
                            localStorage.removeItem('authToken');
                        }
                    });
                } catch (error) {
                    console.error('自动登录失败:', error);
                    localStorage.removeItem('studentInfo');
                    localStorage.removeItem('authToken');
                }
            }
        });

        // 验证token有效性
        async function verifyToken(token) {
            try {
                const response = await fetch(`${API_BASE_URL}/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                return response.ok;
            } catch (error) {
                console.error('Token验证失败:', error);
                return false;
            }
        }

        // ==================== USB控制功能 ====================

        // USB控制功能
        async function controlUSB(enabled) {
            try {
                const result = await require('electron').ipcRenderer.invoke('control-usb', enabled);
                if (result.success) {
                    console.log(`🔌 USB端口${enabled ? '启用' : '禁用'}成功`);
                    addSystemLog('info', `USB端口${enabled ? '启用' : '禁用'}成功`);
                    return true;
                } else {
                    // 不再显示错误日志，避免重复提示
                    console.log(`⚠️ USB端口${enabled ? '启用' : '禁用'}失败: ${result.error}`);
                    return false;
                }
            } catch (error) {
                console.log(`⚠️ USB控制异常: ${error.message}`);
                return false;
            }
        }

        // 检查USB状态
        async function checkUSBStatus() {
            try {
                const result = await require('electron').ipcRenderer.invoke('check-usb-status');
                if (result.success) {
                    return result.enabled;
                } else {
                    console.error('检查USB状态失败:', result.error);
                    return null;
                }
            } catch (error) {
                console.error('检查USB状态异常:', error);
                return null;
            }
        }

        // 检测USB设备
        async function detectUSBDevices() {
            try {
                const result = await require('electron').ipcRenderer.invoke('detect-usb-devices');
                if (result.success) {
                    return result.devices;
                } else {
                    console.error('检测USB设备失败:', result.error);
                    return [];
                }
            } catch (error) {
                console.error('检测USB设备异常:', error);
                return [];
            }
        }

        // USB违规检测和处理
        async function checkUSBViolations() {
            try {
                // 获取系统配置
                const token = localStorage.getItem('authToken');
                if (!token) return;

                const configResponse = await fetch(`${API_BASE_URL}/config`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (configResponse.ok) {
                    const configResult = await configResponse.json();
                    if (configResult.success) {
                        const config = configResult.data;
                        const usbEnabled = config.usbEnabled;

                        console.log(`🔍 USB配置状态: ${usbEnabled ? '允许' : '禁止'}`);

                        // 如果配置禁用USB，则执行控制
                        if (!usbEnabled) {
                            // 检查是否需要尝试USB控制
                            const now = Date.now();
                            const shouldTryUSBControl = (now - lastUSBControlAttempt) > USB_CONTROL_RETRY_INTERVAL;

                            if (shouldTryUSBControl) {
                                // 检查当前USB状态
                                const currentStatus = await checkUSBStatus();
                                if (currentStatus === true) {
                                    console.log('🚫 检测到USB未禁用，尝试禁用...');
                                    addSystemLog('warning', 'USB端口未禁用，尝试禁用...');

                                    lastUSBControlAttempt = now;
                                    const controlResult = await controlUSB(false);
                                    lastUSBControlResult = controlResult;

                                    if (!controlResult) {
                                        console.log('⚠️ USB控制失败，可能需要管理员权限');
                                        addSystemLog('warning', 'USB控制失败，可能需要管理员权限');
                                    }
                                } else if (currentStatus === false) {
                                    console.log('✅ USB端口已禁用');
                                    lastUSBControlResult = true;
                                }
                            } else if (lastUSBControlResult === false) {
                                // 如果之前控制失败，不再重复提示
                                console.log('⏭️ USB控制之前已失败，跳过重试');
                            }

                            // 检测是否有USB设备插入
                            const usbDevices = await detectUSBDevices();
                            if (usbDevices.length > 0) {
                                console.log('🚨 检测到USB设备违规:', usbDevices);
                                addSystemLog('error', `检测到${usbDevices.length}个USB设备违规`);

                                // 过滤出新的USB违规记录（避免重复上报）
                                const newUSBViolations = usbDevices.filter(device => isNewUSBViolation(device));

                                // 立即弹出所有USB设备（无论是否新违规）
                                const ejectResult = await ejectAllUSBDevices();

                                if (newUSBViolations.length > 0) {
                                    console.log(`📝 新USB违规记录 ${newUSBViolations.length} 个:`, newUSBViolations.map(d => d.drive));

                                    // 上报新的USB违规
                                    await reportUSBViolation(newUSBViolations, ejectResult);

                                    // 显示警告
                                    showUSBViolationWarning(newUSBViolations, ejectResult);
                                } else {
                                    console.log('⏭️ 所有USB违规都已在缓存中，跳过上报');
                                }
                            }
                        } else {
                            // 如果配置允许USB，确保USB已启用
                            const currentStatus = await checkUSBStatus();
                            if (currentStatus === false) {
                                console.log('✅ 配置允许USB，正在启用...');
                                addSystemLog('info', '配置允许USB，正在启用...');
                                await controlUSB(true);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('USB违规检测失败:', error);
                addSystemLog('error', `USB违规检测失败: ${error.message}`);
            }
        }

        // 弹出单个USB设备
        async function ejectUSBDevice(driveLetter) {
            try {
                const result = await require('electron').ipcRenderer.invoke('eject-usb-device', driveLetter);
                if (result.success) {
                    console.log(`💿 成功弹出USB设备: ${driveLetter}`);
                    addSystemLog('info', `成功弹出USB设备: ${driveLetter}`);
                    return true;
                } else {
                    console.error(`弹出USB设备失败 ${driveLetter}:`, result.error);
                    addSystemLog('error', `弹出USB设备失败 ${driveLetter}: ${result.error}`);
                    return false;
                }
            } catch (error) {
                console.error(`弹出USB设备异常 ${driveLetter}:`, error);
                addSystemLog('error', `弹出USB设备异常 ${driveLetter}: ${error.message}`);
                return false;
            }
        }

        // 弹出所有USB设备
        async function ejectAllUSBDevices() {
            try {
                console.log('💿 正在弹出所有USB设备...');
                addSystemLog('warning', '检测到USB违规，正在弹出所有USB设备...');

                const result = await require('electron').ipcRenderer.invoke('eject-all-usb-devices');
                if (result.success) {
                    console.log(`📊 USB设备弹出完成: 成功 ${result.successCount} 个, 总计 ${result.totalCount} 个`);
                    addSystemLog('info', `USB设备弹出完成: 成功 ${result.successCount}/${result.totalCount} 个`);
                    return result;
                } else {
                    console.error('批量弹出USB设备失败:', result.error);
                    addSystemLog('error', `批量弹出USB设备失败: ${result.error}`);
                    return { success: false, results: [], totalCount: 0, successCount: 0 };
                }
            } catch (error) {
                console.error('弹出USB设备异常:', error);
                addSystemLog('error', `弹出USB设备异常: ${error.message}`);
                return { success: false, results: [], totalCount: 0, successCount: 0 };
            }
        }

        // 上报USB违规
        async function reportUSBViolation(devices, ejectResult = null) {
            try {
                const token = localStorage.getItem('authToken');
                const studentInfo = JSON.parse(localStorage.getItem('studentInfo') || '{}');
                const studentId = studentInfo.policeId || 'UNKNOWN';

                if (!token) return;

                const violations = devices.map(device => {
                    // 查找对应的弹出结果
                    const ejectInfo = ejectResult?.results?.find(r => r.drive === device.drive);
                    const ejectStatus = ejectInfo ? (ejectInfo.success ? '已弹出' : '弹出失败') : '未弹出';

                    return {
                        type: 'usb_violation',
                        severity: 'high',
                        description: `检测到USB设备插入: ${device.drive} (${ejectStatus})`,
                        details: {
                            deviceType: 'usb_storage',
                            drive: device.drive,
                            totalSize: device.totalSize,
                            freeSpace: device.freeSpace,
                            action: 'detected_and_ejected',
                            ejectResult: ejectInfo || { success: false, error: '未尝试弹出' }
                        },
                        name: `USB设备 ${device.drive}`,
                        pid: 0
                    };
                });

                await fetch(`${API_BASE_URL}/monitoring/violation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        studentId: studentId,
                        violations: violations,
                        timestamp: new Date().toISOString()
                    })
                });

                console.log('📝 USB违规已上报');
                addSystemLog('warning', `已上报${violations.length}个USB违规`);
            } catch (error) {
                console.error('上报USB违规失败:', error);
                addSystemLog('error', `上报USB违规失败: ${error.message}`);
            }
        }

        // 显示USB违规警告
        function showUSBViolationWarning(devices, ejectResult = null) {
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #ff1744;
                color: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0,0,0,0.5);
                z-index: 20000;
                text-align: center;
                font-size: 16px;
                max-width: 400px;
            `;

            // 构建设备列表和弹出状态
            const deviceInfo = devices.map(device => {
                const ejectInfo = ejectResult?.results?.find(r => r.drive === device.drive);
                const status = ejectInfo ? (ejectInfo.success ? '✅已弹出' : '❌弹出失败') : '⚠️未弹出';
                return `${device.drive} ${status}`;
            }).join('<br>');

            const successCount = ejectResult?.successCount || 0;
            const totalCount = devices.length;

            warningDiv.innerHTML = `
                <div style="font-size: 24px; margin-bottom: 15px;">🚫 USB违规警告 🚫</div>
                <div style="font-weight: bold; margin-bottom: 10px;">
                    检测到禁用的USB设备！
                </div>
                <div style="margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">检测到的设备:</div>
                    <div style="font-family: monospace; background: rgba(255,255,255,0.2); padding: 8px; border-radius: 4px; margin-bottom: 10px;">
                        ${deviceInfo}
                    </div>
                    <div style="margin-bottom: 8px;">
                        弹出结果: ${successCount}/${totalCount} 个设备已弹出
                    </div>
                    ${successCount < totalCount ?
                        '<div style="color: #ffeb3b;">⚠️ 部分设备弹出失败，请手动移除！</div>' :
                        '<div style="color: #4caf50;">✅ 所有设备已自动弹出</div>'
                    }
                    <div style="margin-top: 10px;">
                        请确认USB设备已完全移除！<br>
                        继续违规将导致考试终止！
                    </div>
                </div>
                <button onclick="this.parentNode.parentNode.removeChild(this.parentNode)"
                        style="background: white; color: #ff1744; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;">
                    我知道了
                </button>
            `;

            document.body.appendChild(warningDiv);

            // 10秒后自动移除
            setTimeout(() => {
                if (warningDiv.parentNode) {
                    warningDiv.parentNode.removeChild(warningDiv);
                }
            }, 10000);
        }

        // 启动USB控制
        function startUSBControl() {
            console.log('🔌 启动USB控制服务');
            addSystemLog('info', '启动USB控制服务');

            // 立即检查一次
            checkUSBViolations();

            // 每2分钟检查一次USB状态（减少频率）
            setInterval(checkUSBViolations, 120000);
        }
    </script>
</body>
</html>
