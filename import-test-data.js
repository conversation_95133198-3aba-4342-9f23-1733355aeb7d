const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// 数据库路径
const dbPath = path.join(__dirname, 'server', 'database.sqlite');

console.log('📊 导入测试数据:', dbPath);

// 测试数据
const testStudents = [
  {
    name: '张三',
    phone: '13800138001',
    police_id: 'P001',
    department: '技术侦查支队',
    major: '声纹识别',
    disk_serial_number: 'ABC123456789'
  },
  {
    name: '李四',
    phone: '13800138002',
    police_id: 'P002',
    department: '刑事科学技术支队',
    major: '图像处理',
    disk_serial_number: 'DEF123456789'
  },
  {
    name: '王五',
    phone: '13800138003',
    police_id: 'P003',
    department: '网络安全保卫支队',
    major: '电子物证',
    disk_serial_number: 'GHI123456789'
  },
  {
    name: '赵六',
    phone: '13800138004',
    police_id: 'P004',
    department: '刑事科学技术支队',
    major: '痕迹检验',
    disk_serial_number: 'JKL123456789'
  },
  {
    name: '孙七',
    phone: '13800138005',
    police_id: 'P005',
    department: '技术侦查支队',
    major: '网络安全',
    disk_serial_number: 'MNO123456789'
  }
];

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ 数据库连接失败:', err.message);
        return;
    }
    console.log('✅ 数据库连接成功');
});

// 导入数据
async function importTestData() {
    try {
        // 清空现有数据
        await new Promise((resolve, reject) => {
            db.run("DELETE FROM students", (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        console.log('🗑️  清空现有考生数据');

        // 插入测试数据
        const hashedPassword = await bcrypt.hash('123456', 12);
        
        for (const student of testStudents) {
            const id = uuidv4();
            await new Promise((resolve, reject) => {
                db.run(
                    `INSERT INTO students (id, name, phone, police_id, department, major, disk_serial_number, password, created_at, updated_at) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
                    [id, student.name, student.phone, student.police_id, student.department, student.major, student.disk_serial_number, hashedPassword],
                    function(err) {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });
            console.log(`  ✅ 导入: ${student.name} (${student.phone})`);
        }

        console.log('\n🎉 测试数据导入完成！');
        console.log('📝 所有账号密码: 123456');
        console.log('\n📋 测试账号列表:');
        testStudents.forEach((student, index) => {
            console.log(`${index + 1}. ${student.name} - ${student.phone} - ${student.police_id}`);
        });
        
    } catch (error) {
        console.error('❌ 导入失败:', error);
    }
}

// 主函数
async function main() {
    try {
        await importTestData();
    } catch (error) {
        console.error('❌ 主函数失败:', error);
    } finally {
        db.close((err) => {
            if (err) {
                console.error('❌ 关闭数据库失败:', err.message);
            } else {
                console.log('✅ 数据库连接已关闭');
            }
        });
    }
}

// 运行
main();
