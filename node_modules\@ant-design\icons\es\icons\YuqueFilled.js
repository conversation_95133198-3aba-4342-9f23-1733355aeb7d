import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import YuqueFilledSvg from "@ant-design/icons-svg/es/asn/YuqueFilled";
import AntdIcon from "../components/AntdIcon";
var YuqueFilled = function YuqueFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: YuqueFilledSvg
  }));
};

/**![yuque](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDM3MC42Yy05LjktMzkuNCA5LjktMTAyLjIgNzMuNC0xMjQuNGwtNjcuOS0zLjZzLTI1LjctOTAtMTQzLjYtOThjLTExNy45LTguMS0xOTUtMy0xOTUtM3M4Ny40IDU1LjYgNTIuNCAxNTQuN2MtMjUuNiA1Mi41LTY1LjggOTUuNi0xMDguOCAxNDQuNy0xLjMgMS4zLTIuNSAyLjYtMy41IDMuN0MzMTkuNCA2MDUgOTYgODYwIDk2IDg2MGMyNDUuOSA2NC40IDQxMC43LTYuMyA1MDguMi05MS4xIDIwLjUtLjIgMzUuOS0uMyA0Ni4zLS4zIDEzNS44IDAgMjUwLjYtMTE3LjYgMjQ1LjktMjQ4LjQtMy4yLTg5LjktMzEuOS0xMTAuMi00MS44LTE0OS42eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(YuqueFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'YuqueFilled';
}
export default RefIcon;