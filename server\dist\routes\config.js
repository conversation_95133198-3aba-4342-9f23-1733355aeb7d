"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.configRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const models_1 = require("../models");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
exports.configRoutes = router;
// 验证模式
const configSchema = joi_1.default.object({
    blacklistedProcesses: joi_1.default.array().items(joi_1.default.string()).required(),
    usbEnabled: joi_1.default.boolean().required(),
    networkEnabled: joi_1.default.boolean().required(),
    monitoringInterval: joi_1.default.number().integer().min(1000).max(60000).required(),
    heartbeatInterval: joi_1.default.number().integer().min(10000).max(300000).required()
});
// 获取系统配置
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    let config = await models_1.SystemConfigModel.findByPk('default');
    if (!config) {
        // 如果没有配置，创建默认配置
        config = await models_1.SystemConfigModel.create({
            id: 'default',
            blacklisted_processes: JSON.stringify([
                'QQ.exe',
                'WeChat.exe',
                'chrome.exe',
                'firefox.exe',
                'msedge.exe'
            ]),
            usb_enabled: false,
            network_enabled: true,
            monitoring_interval: 5000,
            heartbeat_interval: 30000
        });
    }
    res.json({
        success: true,
        data: {
            id: config.id,
            blacklistedProcesses: config.getBlacklistedProcesses(),
            usbEnabled: config.usb_enabled,
            networkEnabled: config.network_enabled,
            monitoringInterval: config.monitoring_interval,
            heartbeatInterval: config.heartbeat_interval,
            updatedAt: config.updated_at
        }
    });
}));
// 更新系统配置
router.put('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = configSchema.validate(req.body);
    if (error) {
        throw (0, errorHandler_1.createError)(error.details[0].message, 400);
    }
    const { blacklistedProcesses, usbEnabled, networkEnabled, monitoringInterval, heartbeatInterval } = value;
    let config = await models_1.SystemConfigModel.findByPk('default');
    if (!config) {
        // 创建新配置
        config = await models_1.SystemConfigModel.create({
            id: 'default',
            blacklisted_processes: JSON.stringify(blacklistedProcesses),
            usb_enabled: usbEnabled,
            network_enabled: networkEnabled,
            monitoring_interval: monitoringInterval,
            heartbeat_interval: heartbeatInterval
        });
    }
    else {
        // 更新现有配置
        config.setBlacklistedProcesses(blacklistedProcesses);
        config.usb_enabled = usbEnabled;
        config.network_enabled = networkEnabled;
        config.monitoring_interval = monitoringInterval;
        config.heartbeat_interval = heartbeatInterval;
        await config.save();
    }
    res.json({
        success: true,
        data: {
            id: config.id,
            blacklistedProcesses: config.getBlacklistedProcesses(),
            usbEnabled: config.usb_enabled,
            networkEnabled: config.network_enabled,
            monitoringInterval: config.monitoring_interval,
            heartbeatInterval: config.heartbeat_interval,
            updatedAt: config.updated_at
        },
        message: '系统配置更新成功'
    });
}));
// 重置为默认配置
router.post('/reset', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const defaultConfig = {
        blacklisted_processes: JSON.stringify([
            'QQ.exe',
            'WeChat.exe',
            'chrome.exe',
            'firefox.exe',
            'msedge.exe'
        ]),
        usb_enabled: false,
        network_enabled: true,
        monitoring_interval: 5000,
        heartbeat_interval: 30000
    };
    let config = await models_1.SystemConfigModel.findByPk('default');
    if (!config) {
        config = await models_1.SystemConfigModel.create({
            id: 'default',
            ...defaultConfig
        });
    }
    else {
        await config.update(defaultConfig);
    }
    res.json({
        success: true,
        data: {
            id: config.id,
            blacklistedProcesses: config.getBlacklistedProcesses(),
            usbEnabled: config.usb_enabled,
            networkEnabled: config.network_enabled,
            monitoringInterval: config.monitoring_interval,
            heartbeatInterval: config.heartbeat_interval,
            updatedAt: config.updated_at
        },
        message: '配置已重置为默认值'
    });
}));
// 获取黑名单配置
router.get('/blacklist', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    let config = await models_1.SystemConfigModel.findByPk('default');
    if (!config) {
        // 如果没有配置，创建默认配置
        config = await models_1.SystemConfigModel.create({
            id: 'default',
            blacklisted_processes: JSON.stringify([
                'QQ.exe',
                'WeChat.exe',
                'chrome.exe',
                'firefox.exe',
                'msedge.exe',
                'TIM.exe',
                'DingTalk.exe',
                'Steam.exe',
                'Discord.exe',
                'TeamViewer.exe'
            ]),
            usb_enabled: false,
            network_enabled: true,
            monitoring_interval: 5000,
            heartbeat_interval: 30000
        });
    }
    res.json({
        success: true,
        data: {
            blacklistedProcesses: config.getBlacklistedProcesses(),
            lastUpdated: config.updated_at
        }
    });
}));
//# sourceMappingURL=config.js.map