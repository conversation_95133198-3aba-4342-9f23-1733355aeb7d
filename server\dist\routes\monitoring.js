"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.monitoringRoutes = void 0;
const express_1 = require("express");
const joi_1 = __importDefault(require("joi"));
const sequelize_1 = require("sequelize");
const models_1 = require("../models");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
exports.monitoringRoutes = router;
// 验证模式
const querySchema = joi_1.default.object({
    page: joi_1.default.number().integer().min(1).default(1),
    pageSize: joi_1.default.number().integer().min(1).max(100).default(20),
    studentId: joi_1.default.string().uuid().optional(),
    type: joi_1.default.string().optional(),
    severity: joi_1.default.string().valid('low', 'medium', 'high').optional(),
    startDate: joi_1.default.date().optional(),
    endDate: joi_1.default.date().optional()
});
// 获取所有考生状态
router.get('/status', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const statuses = await models_1.StudentStatusModel.findAll({
        include: [{
                model: models_1.StudentModel,
                as: 'student',
                attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
            }],
        order: [['last_heartbeat', 'DESC']]
    });
    const formattedStatuses = statuses.map(status => ({
        studentId: status.student_id,
        student: status.student ? {
            id: status.student.id,
            name: status.student.name,
            phone: status.student.phone,
            policeId: status.student.police_id,
            department: status.student.department,
            major: status.student.major
        } : null,
        isOnline: status.is_online,
        lastHeartbeat: status.last_heartbeat,
        currentProcesses: status.getCurrentProcesses() || [],
        systemInfo: status.getSystemInfo() || {},
        updatedAt: status.updated_at
    }));
    res.json({
        success: true,
        data: formattedStatuses
    });
}));
// 心跳接口
router.post('/heartbeat', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { studentId, systemInfo, processes } = req.body;
    if (!studentId) {
        throw (0, errorHandler_1.createError)('缺少考生ID', 400);
    }
    try {
        // 更新或创建考生状态
        const [status, created] = await models_1.StudentStatusModel.upsert({
            student_id: studentId,
            is_online: true,
            last_heartbeat: new Date(),
            system_info: systemInfo ? JSON.stringify(systemInfo) : null,
            current_processes: processes ? JSON.stringify(processes) : null
        });
        res.json({
            success: true,
            message: '心跳更新成功',
            data: {
                studentId,
                isOnline: true,
                lastHeartbeat: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error('心跳更新失败:', error);
        throw (0, errorHandler_1.createError)('心跳更新失败', 500);
    }
}));
// 状态上报接口
router.post('/status', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { studentId, systemInfo, processes, violations } = req.body;
    if (!studentId) {
        throw (0, errorHandler_1.createError)('缺少考生ID', 400);
    }
    try {
        // 更新考生状态
        await models_1.StudentStatusModel.upsert({
            student_id: studentId,
            is_online: true,
            last_heartbeat: new Date(),
            system_info: systemInfo ? JSON.stringify(systemInfo) : null,
            current_processes: processes ? JSON.stringify(processes) : null
        });
        // 如果有违规记录，保存违规信息
        if (violations && violations.length > 0) {
            for (const violation of violations) {
                await models_1.ViolationModel.create({
                    student_id: studentId,
                    type: violation.type || 'unknown',
                    severity: violation.severity || 'medium',
                    description: violation.description || '未知违规',
                    details: JSON.stringify(violation.details || {}),
                    timestamp: new Date(violation.timestamp || Date.now())
                });
            }
        }
        res.json({
            success: true,
            message: '状态上报成功',
            data: {
                studentId,
                timestamp: new Date().toISOString()
            }
        });
    }
    catch (error) {
        console.error('状态上报失败:', error);
        throw (0, errorHandler_1.createError)('状态上报失败', 500);
    }
}));
// 获取单个考生状态
router.get('/status/:studentId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const status = await models_1.StudentStatusModel.findOne({
        where: { student_id: req.params.studentId },
        include: [{
                model: models_1.StudentModel,
                as: 'student',
                attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
            }]
    });
    if (!status) {
        throw (0, errorHandler_1.createError)('考生状态不存在', 404);
    }
    res.json({
        success: true,
        data: {
            studentId: status.student_id,
            student: status.student ? {
                id: status.student.id,
                name: status.student.name,
                phone: status.student.phone,
                policeId: status.student.police_id,
                department: status.student.department,
                major: status.student.major
            } : null,
            isOnline: status.is_online,
            lastHeartbeat: status.last_heartbeat,
            currentProcesses: status.getCurrentProcesses(),
            systemInfo: status.getSystemInfo(),
            updatedAt: status.updated_at
        }
    });
}));
// 获取违规记录
router.get('/violations', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { error, value } = querySchema.validate(req.query);
    if (error) {
        throw (0, errorHandler_1.createError)(error.details[0].message, 400);
    }
    const { page, pageSize, studentId, type, severity, startDate, endDate } = value;
    const offset = (page - 1) * pageSize;
    // 构建查询条件
    const where = {};
    if (studentId) {
        where.student_id = studentId;
    }
    if (type) {
        where.type = type;
    }
    if (severity) {
        where.severity = severity;
    }
    if (startDate || endDate) {
        where.timestamp = {};
        if (startDate) {
            where.timestamp[sequelize_1.Op.gte] = startDate;
        }
        if (endDate) {
            where.timestamp[sequelize_1.Op.lte] = endDate;
        }
    }
    const { count, rows } = await models_1.ViolationModel.findAndCountAll({
        where,
        include: [{
                model: models_1.StudentModel,
                as: 'student',
                attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
            }],
        limit: pageSize,
        offset,
        order: [['timestamp', 'DESC']]
    });
    const formattedViolations = rows.map(violation => ({
        id: violation.id,
        studentId: violation.student_id,
        student: violation.student ? {
            id: violation.student.id,
            name: violation.student.name,
            phone: violation.student.phone,
            policeId: violation.student.police_id,
            department: violation.student.department,
            major: violation.student.major
        } : null,
        type: violation.type,
        description: violation.description,
        severity: violation.severity,
        timestamp: violation.timestamp,
        createdAt: violation.created_at
    }));
    res.json({
        success: true,
        data: {
            violations: formattedViolations,
            pagination: {
                total: count,
                page,
                pageSize,
                totalPages: Math.ceil(count / pageSize)
            }
        }
    });
}));
// 获取监控统计数据
router.get('/statistics', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    // 总考生数
    const totalStudents = await models_1.StudentModel.count();
    // 在线考生数
    const onlineStudents = await models_1.StudentStatusModel.count({
        where: { is_online: true }
    });
    // 今日违规数
    const todayViolations = await models_1.ViolationModel.count({
        where: {
            timestamp: {
                [sequelize_1.Op.gte]: today
            }
        }
    });
    // 本周违规数
    const weekViolations = await models_1.ViolationModel.count({
        where: {
            timestamp: {
                [sequelize_1.Op.gte]: thisWeek
            }
        }
    });
    // 违规类型统计
    const violationsByType = await models_1.ViolationModel.findAll({
        attributes: [
            'type',
            [models_1.sequelize.fn('COUNT', models_1.sequelize.col('id')), 'count']
        ],
        group: ['type'],
        raw: true
    });
    // 违规严重程度统计
    const violationsBySeverity = await models_1.ViolationModel.findAll({
        attributes: [
            'severity',
            [models_1.sequelize.fn('COUNT', models_1.sequelize.col('id')), 'count']
        ],
        group: ['severity'],
        raw: true
    });
    res.json({
        success: true,
        data: {
            overview: {
                totalStudents,
                onlineStudents,
                offlineStudents: totalStudents - onlineStudents,
                todayViolations,
                weekViolations
            },
            violationsByType,
            violationsBySeverity
        }
    });
}));
//# sourceMappingURL=monitoring.js.map