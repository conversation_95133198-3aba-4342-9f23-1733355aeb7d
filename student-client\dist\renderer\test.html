<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监考系统考生端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 20px;
            width: 100%;
            max-width: 800px;
            text-align: center;
        }

        /* 诚信考试承诺书样式 */
        .agreement-form {
            text-align: left;
            background: white;
            border: 2px solid #2c5aa0;
            border-radius: 8px;
            padding: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .agreement-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 20px;
            text-align: center;
            border-radius: 6px 6px 0 0;
        }

        .agreement-header h1 {
            margin: 0;
            font-size: 24px;
            color: #2c5aa0;
            font-weight: bold;
        }

        .agreement-content {
            padding: 25px;
            line-height: 1.6;
            color: #333;
        }

        .intro-text {
            margin-bottom: 20px;
            font-size: 14px;
            text-indent: 2em;
        }

        .agreement-items {
            margin-bottom: 25px;
        }

        .agreement-items p {
            margin-bottom: 15px;
            font-size: 14px;
            text-indent: 2em;
            line-height: 1.8;
        }

        .agreement-footer {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .checkbox-container {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            font-size: 13px;
            line-height: 1.5;
            color: #495057;
        }

        .checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 2px;
            transform: scale(1.2);
        }

        .agreement-buttons {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
            border-radius: 0 0 6px 6px;
        }

        .agree-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 15px;
            transition: background 0.3s;
        }

        .agree-btn:hover:not(:disabled) {
            background: #c82333;
        }

        .agree-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .reject-btn {
            background: #2c3e50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .reject-btn:hover {
            background: #1a252f;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 诚信考试承诺书 -->
        <div id="agreementForm" class="agreement-form">
            <div class="agreement-header">
                <h1>诚信考试承诺书</h1>
            </div>
            
            <div class="agreement-content">
                <p class="intro-text">
                    我是参加"<strong>2025年全省电子物证技能力测评</strong>"的考生，愿意在考试中自觉遵守各项考试规定，现作重要承诺：
                </p>
                
                <div class="agreement-items">
                    <p><strong>一、</strong>同意考试组委会对本人考试全程进行监考，考试所用设备均主动按照组委会要求安装远程监考软件，主动配合组委会实现监考巡考。</p>
                    
                    <p><strong>二、</strong>严格遵守能力考核考试规则，保证按规定的程序和要求参加考试，如有违反，自愿承担作弊后果。</p>
                    
                    <p><strong>三、</strong>服从组委会的管理，自觉遵守考试纪律。</p>
                    
                    <p><strong>四、</strong>不使用除考试设备外的通讯工具。</p>
                    
                    <p><strong>五、</strong>考试时独立思考，不抄袭或有意让他人抄袭答案。</p>
                </div>
                
                <div class="agreement-footer">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeCheckbox">
                        本人已认真阅读以上诚信承诺规则，并保证严格遵守以上常规律，共同维护此次承诺执行，做一名诚实守信的参赛选手。如有违反，自愿承担相应责任和由此造成的一切后果。
                    </label>
                </div>
            </div>
            
            <div class="agreement-buttons">
                <button id="agreeBtn" class="agree-btn" disabled>同意</button>
                <button id="rejectBtn" class="reject-btn">拒绝</button>
            </div>
        </div>
    </div>

    <script>
        const agreeCheckbox = document.getElementById('agreeCheckbox');
        const agreeBtn = document.getElementById('agreeBtn');
        const rejectBtn = document.getElementById('rejectBtn');

        agreeCheckbox.addEventListener('change', function() {
            agreeBtn.disabled = !this.checked;
        });

        agreeBtn.addEventListener('click', function() {
            if (agreeCheckbox.checked) {
                alert('已同意承诺书，将进入登录界面');
                // 这里可以跳转到登录界面
            }
        });

        rejectBtn.addEventListener('click', function() {
            if (confirm('拒绝承诺书将无法参加考试，确定要退出吗？')) {
                window.close();
            }
        });
    </script>
</body>
</html>
