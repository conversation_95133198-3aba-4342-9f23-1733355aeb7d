"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Violation = exports.ViolationInstance = void 0;
const sequelize_1 = require("sequelize");
class ViolationInstance extends sequelize_1.Model {
}
exports.ViolationInstance = ViolationInstance;
const Violation = (sequelize) => {
    ViolationInstance.init({
        id: {
            type: sequelize_1.DataTypes.UUID,
            defaultValue: sequelize_1.DataTypes.UUIDV4,
            primaryKey: true
        },
        student_id: {
            type: sequelize_1.DataTypes.UUID,
            allowNull: false,
            references: {
                model: 'students',
                key: 'id'
            },
            comment: '考生ID'
        },
        type: {
            type: sequelize_1.DataTypes.STRING(50),
            allowNull: false,
            comment: '违规类型'
        },
        description: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: false,
            comment: '违规描述'
        },
        severity: {
            type: sequelize_1.DataTypes.ENUM('low', 'medium', 'high'),
            allowNull: false,
            defaultValue: 'medium',
            comment: '严重程度'
        },
        details: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            comment: '违规详细信息(JSON)'
        },
        timestamp: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            comment: '违规时间'
        }
    }, {
        sequelize,
        modelName: 'Violation',
        tableName: 'violations',
        indexes: [
            {
                fields: ['student_id']
            },
            {
                fields: ['type']
            },
            {
                fields: ['timestamp']
            },
            {
                fields: ['severity']
            }
        ]
    });
    return ViolationInstance;
};
exports.Violation = Violation;
//# sourceMappingURL=Violation.js.map