"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const models_1 = require("./models");
const auth_1 = require("./routes/auth");
const students_1 = require("./routes/students");
const config_1 = require("./routes/config");
const monitoring_1 = require("./routes/monitoring");
const errorHandler_1 = require("./middleware/errorHandler");
const rateLimiter_1 = require("./middleware/rateLimiter");
const websocket_1 = require("./services/websocket");
// 加载环境变量
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:5173",
        methods: ["GET", "POST"]
    }
});
const PORT = process.env.PORT || 3000;
// 中间件
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || "http://localhost:5173",
    credentials: true
}));
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// 速率限制
app.use(rateLimiter_1.rateLimiter);
// 路由
app.use('/api/auth', auth_1.authRoutes);
app.use('/api/students', students_1.studentRoutes);
app.use('/api/config', config_1.configRoutes);
app.use('/api/monitoring', monitoring_1.monitoringRoutes);
// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
// 错误处理
app.use(errorHandler_1.errorHandler);
// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});
// WebSocket设置
(0, websocket_1.setupWebSocket)(io);
// 启动服务器
const startServer = async () => {
    try {
        // 初始化数据库 (不强制重建表)
        await (0, models_1.syncDatabase)(false);
        server.listen(PORT, () => {
            console.log(`服务器运行在端口 ${PORT}`);
            console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
            console.log(`数据库: ${process.env.DATABASE_URL || './database.sqlite'}`);
        });
    }
    catch (error) {
        console.error('服务器启动失败:', error);
        process.exit(1);
    }
};
// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
startServer();
//# sourceMappingURL=index.js.map