import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { Op } from 'sequelize';
import { ViolationModel, StudentStatusModel, StudentModel, sequelize } from '../models';
import { createError, asyncHandler } from '../middleware/errorHandler';

const router = Router();

// 验证模式
const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  pageSize: Joi.number().integer().min(1).max(100).default(20),
  studentId: Joi.string().uuid().optional(),
  type: Joi.string().optional(),
  severity: Joi.string().valid('low', 'medium', 'high').optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional()
});

// 获取所有考生状态
router.get('/status', asyncHandler(async (req: any, res: any) => {
  const statuses = await StudentStatusModel.findAll({
    include: [{
      model: StudentModel,
      as: 'student',
      attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
    }],
    order: [['last_heartbeat', 'DESC']]
  });

  const formattedStatuses = statuses.map(status => ({
    studentId: status.student_id,
    student: (status as any).student ? {
      id: (status as any).student.id,
      name: (status as any).student.name,
      phone: (status as any).student.phone,
      policeId: (status as any).student.police_id,
      department: (status as any).student.department,
      major: (status as any).student.major
    } : null,
    isOnline: status.is_online,
    lastHeartbeat: status.last_heartbeat,
    currentProcesses: status.getCurrentProcesses() || [],
    systemInfo: status.getSystemInfo() || {},
    updatedAt: status.updated_at
  }));

  res.json({
    success: true,
    data: formattedStatuses
  });
}));

// 心跳接口
router.post('/heartbeat', asyncHandler(async (req: any, res: any) => {
  const { studentId, systemInfo, processes } = req.body;

  if (!studentId) {
    throw createError('缺少考生ID', 400);
  }

  try {
    // 更新或创建考生状态
    const [status, created] = await StudentStatusModel.upsert({
      student_id: studentId,
      is_online: true,
      last_heartbeat: new Date(),
      system_info: systemInfo ? JSON.stringify(systemInfo) : null,
      current_processes: processes ? JSON.stringify(processes) : null
    });

    res.json({
      success: true,
      message: '心跳更新成功',
      data: {
        studentId,
        isOnline: true,
        lastHeartbeat: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('心跳更新失败:', error);
    throw createError('心跳更新失败', 500);
  }
}));

// 状态上报接口
router.post('/status', asyncHandler(async (req: any, res: any) => {
  const { studentId, systemInfo, processes, violations } = req.body;

  if (!studentId) {
    throw createError('缺少考生ID', 400);
  }

  try {
    // 更新考生状态
    await StudentStatusModel.upsert({
      student_id: studentId,
      is_online: true,
      last_heartbeat: new Date(),
      system_info: systemInfo ? JSON.stringify(systemInfo) : null,
      current_processes: processes ? JSON.stringify(processes) : null
    });

    // 如果有违规记录，保存违规信息
    if (violations && violations.length > 0) {
      for (const violation of violations) {
        await ViolationModel.create({
          student_id: studentId,
          type: violation.type || 'unknown',
          severity: violation.severity || 'medium',
          description: violation.description || '未知违规',
          details: JSON.stringify(violation.details || {}),
          timestamp: new Date(violation.timestamp || Date.now())
        });
      }
    }

    res.json({
      success: true,
      message: '状态上报成功',
      data: {
        studentId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('状态上报失败:', error);
    throw createError('状态上报失败', 500);
  }
}));

// 获取单个考生状态
router.get('/status/:studentId', asyncHandler(async (req: any, res: any) => {
  const status = await StudentStatusModel.findOne({
    where: { student_id: req.params.studentId },
    include: [{
      model: StudentModel,
      as: 'student',
      attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
    }]
  });

  if (!status) {
    throw createError('考生状态不存在', 404);
  }

  res.json({
    success: true,
    data: {
      studentId: status.student_id,
      student: (status as any).student ? {
        id: (status as any).student.id,
        name: (status as any).student.name,
        phone: (status as any).student.phone,
        policeId: (status as any).student.police_id,
        department: (status as any).student.department,
        major: (status as any).student.major
      } : null,
      isOnline: status.is_online,
      lastHeartbeat: status.last_heartbeat,
      currentProcesses: status.getCurrentProcesses(),
      systemInfo: status.getSystemInfo(),
      updatedAt: status.updated_at
    }
  });
}));

// 获取违规记录
router.get('/violations', asyncHandler(async (req: any, res: any) => {
  const { error, value } = querySchema.validate(req.query);
  if (error) {
    throw createError(error.details[0].message, 400);
  }

  const { page, pageSize, studentId, type, severity, startDate, endDate } = value;
  const offset = (page - 1) * pageSize;

  // 构建查询条件
  const where: any = {};
  if (studentId) {
    where.student_id = studentId;
  }
  if (type) {
    where.type = type;
  }
  if (severity) {
    where.severity = severity;
  }
  if (startDate || endDate) {
    where.timestamp = {};
    if (startDate) {
      where.timestamp[Op.gte] = startDate;
    }
    if (endDate) {
      where.timestamp[Op.lte] = endDate;
    }
  }

  const { count, rows } = await ViolationModel.findAndCountAll({
    where,
    include: [{
      model: StudentModel,
      as: 'student',
      attributes: ['id', 'name', 'phone', 'police_id', 'department', 'major']
    }],
    limit: pageSize,
    offset,
    order: [['timestamp', 'DESC']]
  });

  const formattedViolations = rows.map(violation => ({
    id: violation.id,
    studentId: violation.student_id,
    student: (violation as any).student ? {
      id: (violation as any).student.id,
      name: (violation as any).student.name,
      phone: (violation as any).student.phone,
      policeId: (violation as any).student.police_id,
      department: (violation as any).student.department,
      major: (violation as any).student.major
    } : null,
    type: violation.type,
    description: violation.description,
    severity: violation.severity,
    timestamp: violation.timestamp,
    createdAt: violation.created_at
  }));

  res.json({
    success: true,
    data: {
      violations: formattedViolations,
      pagination: {
        total: count,
        page,
        pageSize,
        totalPages: Math.ceil(count / pageSize)
      }
    }
  });
}));

// 获取监控统计数据
router.get('/statistics', asyncHandler(async (req: any, res: any) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  // 总考生数
  const totalStudents = await StudentModel.count();

  // 在线考生数
  const onlineStudents = await StudentStatusModel.count({
    where: { is_online: true }
  });

  // 今日违规数
  const todayViolations = await ViolationModel.count({
    where: {
      timestamp: {
        [Op.gte]: today
      }
    }
  });

  // 本周违规数
  const weekViolations = await ViolationModel.count({
    where: {
      timestamp: {
        [Op.gte]: thisWeek
      }
    }
  });

  // 违规类型统计
  const violationsByType = await ViolationModel.findAll({
    attributes: [
      'type',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['type'],
    raw: true
  }) as any[];

  // 违规严重程度统计
  const violationsBySeverity = await ViolationModel.findAll({
    attributes: [
      'severity',
      [sequelize.fn('COUNT', sequelize.col('id')), 'count']
    ],
    group: ['severity'],
    raw: true
  }) as any[];

  res.json({
    success: true,
    data: {
      overview: {
        totalStudents,
        onlineStudents,
        offlineStudents: totalStudents - onlineStudents,
        todayViolations,
        weekViolations
      },
      violationsByType,
      violationsBySeverity
    }
  });
}));

export { router as monitoringRoutes };
