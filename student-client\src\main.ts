import { app, BrowserWindow, ipc<PERSON>ain, dialog, Menu } from 'electron';
import * as path from 'path';
import { SystemMonitor } from './main/SystemMonitor';
import { NetworkController } from './main/NetworkController';
import { USBController } from './main/USBController';
import { ProcessManager } from './main/ProcessManager';
import { AuthService } from './main/AuthService';
import { WebSocketClient } from './main/WebSocketClient';

class ExamMonitorApp {
  private mainWindow: BrowserWindow | null = null;
  private systemMonitor: SystemMonitor;
  private networkController: NetworkController;
  private usbController: USBController;
  private processManager: ProcessManager;
  private authService: AuthService;
  private wsClient: WebSocketClient;

  constructor() {
    this.systemMonitor = new SystemMonitor();
    this.networkController = new NetworkController();
    this.usbController = new USBController();
    this.processManager = new ProcessManager();
    this.authService = new AuthService();
    this.wsClient = new WebSocketClient();

    this.initializeApp();
  }

  private initializeApp() {
    // 确保单实例运行
    const gotTheLock = app.requestSingleInstanceLock();
    if (!gotTheLock) {
      app.quit();
      return;
    }

    app.on('second-instance', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) this.mainWindow.restore();
        this.mainWindow.focus();
      }
    });

    app.whenReady().then(() => {
      this.createWindow();
      this.setupIpcHandlers();
      this.setupMenu();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // 阻止应用退出
    app.on('before-quit', (event) => {
      if (this.wsClient.isConnected()) {
        event.preventDefault();
        dialog.showMessageBox(this.mainWindow!, {
          type: 'warning',
          title: '警告',
          message: '考试期间不允许退出监考程序',
          buttons: ['确定']
        });
      }
    });
  }

  private createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      title: '监考系统考生端',
      show: true  // 直接显示窗口
    });

    // 加载渲染进程页面
    const htmlPath = path.join(__dirname, './renderer/simple.html');
    console.log('Loading HTML from:', htmlPath);

    this.mainWindow.loadFile(htmlPath).catch(err => {
      console.error('Failed to load HTML:', err);
    });

    // 总是打开开发者工具来调试
    this.mainWindow.webContents.openDevTools();

    this.mainWindow.once('ready-to-show', () => {
      console.log('Window ready to show');
      this.mainWindow!.show();

      // 考试期间禁用某些功能
      this.mainWindow!.setMenuBarVisibility(false);
      // this.mainWindow!.setAlwaysOnTop(true);  // 暂时禁用置顶，方便调试
    });

    // 添加更多事件监听来调试
    this.mainWindow.webContents.on('did-finish-load', () => {
      console.log('Page finished loading');
    });

    this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      console.error('Page failed to load:', errorCode, errorDescription);
    });

    this.mainWindow.webContents.on('dom-ready', () => {
      console.log('DOM ready');
    });

    // 阻止新窗口打开
    this.mainWindow.webContents.setWindowOpenHandler(() => {
      return { action: 'deny' };
    });

    // 阻止导航到外部链接
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);
      if (parsedUrl.origin !== 'http://localhost:3001' && parsedUrl.origin !== 'file://') {
        event.preventDefault();
      }
    });
  }

  private setupMenu() {
    // 禁用默认菜单
    Menu.setApplicationMenu(null);
  }

  private setupIpcHandlers() {
    // 系统信息获取
    ipcMain.handle('get-system-info', async () => {
      return await this.systemMonitor.getSystemInfo();
    });

    // 获取运行进程
    ipcMain.handle('get-running-processes', async () => {
      return await this.systemMonitor.getRunningProcesses();
    });

    // 身份验证
    ipcMain.handle('authenticate', async (_, credentials) => {
      return await this.authService.authenticate(credentials);
    });

    // 硬件验证
    ipcMain.handle('verify-hardware', async () => {
      const systemInfo = await this.systemMonitor.getSystemInfo();
      return await this.authService.verifyHardware(systemInfo.diskSerialNumber);
    });

    // WebSocket连接
    ipcMain.handle('connect-websocket', async (_, token) => {
      return await this.wsClient.connect(token);
    });

    // 断开WebSocket连接
    ipcMain.handle('disconnect-websocket', async () => {
      this.wsClient.disconnect();
    });

    // 发送心跳
    ipcMain.handle('send-heartbeat', async () => {
      const systemInfo = await this.systemMonitor.getSystemInfo();
      const processes = await this.systemMonitor.getRunningProcesses();
      
      this.wsClient.sendHeartbeat({
        systemInfo,
        processes
      });
    });

    // 网络控制
    ipcMain.handle('set-network-enabled', async (_, enabled, serverURL) => {
      return await this.networkController.setNetworkEnabled(enabled, serverURL);
    });

    // USB控制
    ipcMain.handle('set-usb-enabled', async (_, enabled) => {
      return await this.usbController.setUSBEnabled(enabled);
    });

    // 进程管理
    ipcMain.handle('kill-process', async (_, pid) => {
      return await this.processManager.killProcess(pid);
    });

    // 检查黑名单进程
    ipcMain.handle('check-blacklisted-processes', async (_, blacklist) => {
      const processes = await this.systemMonitor.getRunningProcesses();
      const violations = this.processManager.checkBlacklistedProcesses(processes, blacklist);

      // 如果有违规进程，发送违规报告
      if (violations.length > 0) {
        violations.forEach(violation => {
          this.wsClient.sendViolationReport(violation);
        });
      }

      return violations;
    });

    // 窗口控制
    ipcMain.handle('minimize-window', () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
        return true;
      }
      return false;
    });

    ipcMain.handle('restore-window', () => {
      if (this.mainWindow) {
        this.mainWindow.restore();
        this.mainWindow.show();
        this.mainWindow.focus();
        return true;
      }
      return false;
    });

    ipcMain.handle('hide-window', () => {
      if (this.mainWindow) {
        this.mainWindow.hide();
        return true;
      }
      return false;
    });

    ipcMain.handle('show-window', () => {
      if (this.mainWindow) {
        this.mainWindow.show();
        this.mainWindow.focus();
        return true;
      }
      return false;
    });

    // 退出应用
    ipcMain.handle('quit-app', () => {
      app.quit();
      return true;
    });

    // 获取应用状态
    ipcMain.handle('get-app-status', () => {
      return {
        isConnected: this.wsClient.isConnected(),
        networkEnabled: this.networkController.isNetworkEnabled(),
        usbEnabled: this.usbController.isUSBEnabled()
      };
    });

    // WebSocket事件转发到渲染进程
    this.wsClient.on('config-update', (config) => {
      this.mainWindow?.webContents.send('config-update', config);
    });

    this.wsClient.on('force-disconnect', (data) => {
      this.mainWindow?.webContents.send('force-disconnect', data);
      // 强制断开后退出应用
      setTimeout(() => {
        app.quit();
      }, 3000);
    });

    this.wsClient.on('connection-status', (status) => {
      this.mainWindow?.webContents.send('connection-status', status);
    });
  }
}

// 创建应用实例
new ExamMonitorApp();
