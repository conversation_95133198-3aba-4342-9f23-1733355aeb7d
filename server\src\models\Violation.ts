import { DataTypes, Model, Sequelize } from 'sequelize';

export interface ViolationAttributes {
  id: string;
  student_id: string;
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  details?: string; // JSON字符串，存储违规详细信息
  timestamp: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface ViolationCreationAttributes extends Omit<ViolationAttributes, 'id' | 'created_at' | 'updated_at'> {}

export class ViolationInstance extends Model<ViolationAttributes, ViolationCreationAttributes> implements ViolationAttributes {
  public id!: string;
  public student_id!: string;
  public type!: string;
  public description!: string;
  public severity!: 'low' | 'medium' | 'high';
  public details?: string;
  public timestamp!: Date;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
}

export const Violation = (sequelize: Sequelize) => {
  ViolationInstance.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
      },
      student_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'students',
          key: 'id'
        },
        comment: '考生ID'
      },
      type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '违规类型'
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '违规描述'
      },
      severity: {
        type: DataTypes.ENUM('low', 'medium', 'high'),
        allowNull: false,
        defaultValue: 'medium',
        comment: '严重程度'
      },
      details: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '违规详细信息(JSON)'
      },
      timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: '违规时间'
      }
    },
    {
      sequelize,
      modelName: 'Violation',
      tableName: 'violations',
      indexes: [
        {
          fields: ['student_id']
        },
        {
          fields: ['type']
        },
        {
          fields: ['timestamp']
        },
        {
          fields: ['severity']
        }
      ]
    }
  );

  return ViolationInstance;
};
