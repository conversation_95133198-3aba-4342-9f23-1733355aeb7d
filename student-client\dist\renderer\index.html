<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监考系统考生端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 95%;
            max-width: 800px;
            text-align: center;
            position: relative;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .login-form {
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .system-info {
            display: none;
            text-align: left;
            margin-top: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: #333;
        }

        .info-value {
            color: #666;
            font-family: monospace;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* 新增样式 */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #667eea;
        }

        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
        }

        .status-offline {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .logs-section {
            margin-top: 20px;
            text-align: left;
        }

        .logs-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            height: 150px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        .log-entry {
            margin-bottom: 3px;
            padding: 1px 0;
        }

        .log-time {
            color: #6c757d;
            margin-right: 8px;
        }

        .log-level-info {
            color: #17a2b8;
        }

        .log-level-warning {
            color: #ffc107;
        }

        .log-level-error {
            color: #dc3545;
        }

        .minimized-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        /* 诚信考试承诺书样式 */
        .agreement-form {
            display: block;
            text-align: left;
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border: 2px solid #2c5aa0;
            border-radius: 8px;
            padding: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .agreement-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 20px;
            text-align: center;
            border-radius: 6px 6px 0 0;
        }

        .agreement-header h1 {
            margin: 0;
            font-size: 24px;
            color: #2c5aa0;
            font-weight: bold;
        }

        .agreement-content {
            padding: 25px;
            line-height: 1.6;
            color: #333;
        }

        .intro-text {
            margin-bottom: 20px;
            font-size: 14px;
            text-indent: 2em;
        }

        .agreement-items {
            margin-bottom: 25px;
        }

        .agreement-items p {
            margin-bottom: 15px;
            font-size: 14px;
            text-indent: 2em;
            line-height: 1.8;
        }

        .agreement-footer {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .checkbox-container {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            font-size: 13px;
            line-height: 1.5;
            color: #495057;
        }

        .checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 2px;
            transform: scale(1.2);
        }

        .agreement-buttons {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
            border-radius: 0 0 6px 6px;
        }

        .agree-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 15px;
            transition: background 0.3s;
        }

        .agree-btn:hover:not(:disabled) {
            background: #c82333;
        }

        .agree-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .reject-btn {
            background: #2c3e50;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .reject-btn:hover {
            background: #1a252f;
        }

        /* 默认隐藏非承诺书元素 */
        .default-header {
            display: none;
        }

        .show-default .default-header {
            display: block;
        }

        .show-default .agreement-form {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 诚信考试承诺书 -->
        <div id="agreementForm" class="agreement-form">
            <div class="agreement-header">
                <h1>诚信考试承诺书</h1>
            </div>

            <div class="agreement-content">
                <p class="intro-text">
                    我是参加"<strong>2025年全省电子物证技能力测评</strong>"的考生，愿意在考试中自觉遵守各项考试规定，现作重要承诺：
                </p>

                <div class="agreement-items">
                    <p><strong>一、</strong>同意考试组委会对本人考试全程进行监考，考试所用设备均主动按照组委会要求安装远程监考软件，主动配合组委会实现监考巡考。</p>

                    <p><strong>二、</strong>严格遵守能力考核考试规则，保证按规定的程序和要求参加考试，如有违反，自愿承担作弊后果。</p>

                    <p><strong>三、</strong>服从组委会的管理，自觉遵守考试纪律。</p>

                    <p><strong>四、</strong>不使用除考试设备外的通讯工具。</p>

                    <p><strong>五、</strong>考试时独立思考，不抄袭或有意让他人抄袭答案。</p>
                </div>

                <div class="agreement-footer">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeCheckbox">
                        本人已认真阅读以上诚信承诺规则，并保证严格遵守以上常规律，共同维护此次承诺执行，做一名诚实守信的参赛选手。如有违反，自愿承担相应责任和由此造成的一切后果。
                    </label>
                </div>
            </div>

            <div class="agreement-buttons">
                <button id="agreeBtn" class="agree-btn" disabled>同意</button>
                <button id="rejectBtn" class="reject-btn">拒绝</button>
            </div>
        </div>

        <div class="default-header">
            <div class="logo">监</div>
            <h1>监考系统考生端</h1>
            <p class="subtitle">请完成身份验证以开始考试</p>

            <div id="status" class="status connecting">
                <div class="loading"></div>
                正在初始化系统...
            </div>
        </div>

        <div id="loginForm" class="login-form" style="display: none;">
            <div class="form-group">
                <label for="phone">手机号码</label>
                <input type="tel" id="phone" placeholder="请输入手机号码">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="请输入密码">
            </div>
            <button id="loginBtn" class="btn">登录</button>
            <p style="margin-top: 15px; color: #666; font-size: 14px;">
                如果您的设备已注册，系统将自动验证硬件信息
            </p>
        </div>

        <div id="systemInfo" class="system-info">
            <h3 style="margin-bottom: 20px; color: #333; text-align: center;">监考系统状态</h3>

            <!-- 统计信息 -->
            <div class="stats-row">
                <div class="stat-item">
                    <span class="stat-value" id="uptime">00:00:00</span>
                    <div class="stat-label">运行时间</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="processCount">0</span>
                    <div class="stat-label">监控进程</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="violationCount">0</span>
                    <div class="stat-label">违规次数</div>
                </div>
            </div>

            <!-- 信息卡片 -->
            <div class="info-grid">
                <div class="info-card">
                    <h4>👤 考生信息</h4>
                    <div class="info-item">
                        <span class="info-label">姓名:</span>
                        <span class="info-value" id="studentName">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">警号:</span>
                        <span class="info-value" id="policeId">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">单位:</span>
                        <span class="info-value" id="department">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">专业:</span>
                        <span class="info-value" id="major">-</span>
                    </div>
                </div>

                <div class="info-card">
                    <h4>🔗 连接状态</h4>
                    <div class="info-item">
                        <span class="info-label">服务器:</span>
                        <span class="info-value" id="connectionStatus">
                            <span class="status-indicator status-offline"></span>未连接
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">网络:</span>
                        <span class="info-value" id="networkStatus">
                            <span class="status-indicator status-online"></span>已连接
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">监控:</span>
                        <span class="info-value" id="monitorStatus">
                            <span class="status-indicator status-online"></span>正常
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">USB:</span>
                        <span class="info-value" id="usbStatus">
                            <span class="status-indicator status-warning"></span>已禁用
                        </span>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="info-grid">
                <div class="info-card">
                    <h4>💻 系统信息</h4>
                    <div class="info-item">
                        <span class="info-label">操作系统:</span>
                        <span class="info-value" id="osVersion">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计算机名:</span>
                        <span class="info-value" id="computerName">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">IP地址:</span>
                        <span class="info-value" id="ipAddress">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">硬盘序列号:</span>
                        <span class="info-value" id="diskSerial">-</span>
                    </div>
                </div>

                <div class="info-card">
                    <h4>⚡ 实时监控</h4>
                    <div class="info-item">
                        <span class="info-label">CPU使用率:</span>
                        <span class="info-value" id="cpuUsage">0%</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">内存使用率:</span>
                        <span class="info-value" id="memoryUsage">0%</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">活动进程:</span>
                        <span class="info-value" id="activeProcesses">0</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后心跳:</span>
                        <span class="info-value" id="lastHeartbeat">-</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn-secondary" onclick="refreshStatus()">刷新状态</button>
                <button class="btn-secondary" onclick="showLogs()">查看日志</button>
                <button class="btn-secondary" onclick="minimizeWindow()">最小化</button>
            </div>

            <!-- 日志区域 -->
            <div class="logs-section" id="logsSection" style="display: none;">
                <h4 style="margin-bottom: 10px; color: #333;">系统日志</h4>
                <div class="logs-container" id="logsContainer">
                    <!-- 日志内容将在这里动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 最小化指示器 -->
    <div class="minimized-indicator" id="minimizedIndicator">
        🔍 监考系统运行中 - 点击恢复
    </div>

    <script>
        // 应用状态
        let isAuthenticated = false;
        let systemInfo = null;
        let studentInfo = null;
        let startTime = Date.now();
        let processCount = 0;
        let violationCount = 0;
        let isMinimized = false;
        let logs = [];

        // DOM元素
        const statusEl = document.getElementById('status');
        const loginFormEl = document.getElementById('loginForm');
        const systemInfoEl = document.getElementById('systemInfo');
        const phoneInput = document.getElementById('phone');
        const passwordInput = document.getElementById('password');
        const loginBtn = document.getElementById('loginBtn');
        const minimizedIndicator = document.getElementById('minimizedIndicator');

        // 承诺书相关元素
        const agreementFormEl = document.getElementById('agreementForm');
        const agreeCheckbox = document.getElementById('agreeCheckbox');
        const agreeBtn = document.getElementById('agreeBtn');
        const rejectBtn = document.getElementById('rejectBtn');

        // 更新状态显示
        function updateStatus(message, type = 'connecting') {
            statusEl.className = `status ${type}`;
            statusEl.innerHTML = type === 'connecting' ? 
                `<div class="loading"></div>${message}` : message;
        }

        // 承诺书相关函数
        function showAgreementForm() {
            document.querySelector('.container').classList.remove('show-default');
            agreementFormEl.style.display = 'block';
        }

        function hideAgreementForm() {
            document.querySelector('.container').classList.add('show-default');
            agreementFormEl.style.display = 'none';
        }

        function handleAgreeCheckbox() {
            agreeBtn.disabled = !agreeCheckbox.checked;
        }

        function handleAgree() {
            if (!agreeCheckbox.checked) {
                alert('请先勾选承诺书确认选项');
                return;
            }

            // 隐藏承诺书，显示登录界面
            hideAgreementForm();
            showLoginForm();
            addLog('info', '用户已同意诚信考试承诺书');
        }

        function handleReject() {
            const confirmed = confirm('拒绝承诺书将无法参加考试，确定要退出吗？');
            if (confirmed) {
                addLog('warning', '用户拒绝诚信考试承诺书，程序退出');
                if (window.electronAPI && window.electronAPI.quitApp) {
                    window.electronAPI.quitApp();
                } else {
                    window.close();
                }
            }
        }

        // 显示登录表单
        function showLoginForm() {
            loginFormEl.style.display = 'block';
            updateStatus('请输入登录信息', 'connecting');
        }

        // 显示系统信息
        function showSystemInfo() {
            systemInfoEl.style.display = 'block';
            loginFormEl.style.display = 'none';
            updateStatus('系统运行正常', 'connected');

            if (studentInfo) {
                document.getElementById('studentName').textContent = studentInfo.name;
                document.getElementById('policeId').textContent = studentInfo.policeId;
                document.getElementById('department').textContent = studentInfo.department;
                document.getElementById('major').textContent = studentInfo.major || '-';
            }

            if (systemInfo) {
                document.getElementById('osVersion').textContent = systemInfo.osVersion || '-';
                document.getElementById('computerName').textContent = systemInfo.computerName || '-';
                document.getElementById('ipAddress').textContent = systemInfo.ipAddress || '-';
                document.getElementById('diskSerial').textContent = systemInfo.diskSerialNumber || '-';
            }

            // 启动定时器
            startTimers();
        }

        // 启动定时器
        function startTimers() {
            // 更新运行时间
            setInterval(updateUptime, 1000);

            // 更新系统状态
            setInterval(updateSystemStats, 5000);

            // 更新心跳时间
            setInterval(updateHeartbeat, 1000);
        }

        // 更新运行时间
        function updateUptime() {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);

            const uptimeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('uptime').textContent = uptimeStr;
        }

        // 更新系统统计
        function updateSystemStats() {
            document.getElementById('processCount').textContent = processCount;
            document.getElementById('violationCount').textContent = violationCount;

            // 模拟CPU和内存使用率
            const cpuUsage = Math.floor(Math.random() * 30) + 10;
            const memoryUsage = Math.floor(Math.random() * 40) + 30;
            const activeProcesses = Math.floor(Math.random() * 50) + 100;

            document.getElementById('cpuUsage').textContent = `${cpuUsage}%`;
            document.getElementById('memoryUsage').textContent = `${memoryUsage}%`;
            document.getElementById('activeProcesses').textContent = activeProcesses;
        }

        // 更新心跳时间
        function updateHeartbeat() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN');
            document.getElementById('lastHeartbeat').textContent = timeStr;
        }

        // 添加日志
        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString('zh-CN');
            const logEntry = {
                time: timestamp,
                level: level,
                message: message
            };

            logs.unshift(logEntry);
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }

            updateLogsDisplay();
        }

        // 更新日志显示
        function updateLogsDisplay() {
            const logsContainer = document.getElementById('logsContainer');
            if (!logsContainer) return;

            logsContainer.innerHTML = logs.map(log =>
                `<div class="log-entry">
                    <span class="log-time">${log.time}</span>
                    <span class="log-level-${log.level}">[${log.level.toUpperCase()}]</span>
                    ${log.message}
                </div>`
            ).join('');

            logsContainer.scrollTop = 0;
        }

        // 刷新状态
        function refreshStatus() {
            addLog('info', '手动刷新系统状态');
            updateSystemStats();

            // 重新获取系统信息
            if (window.electronAPI && window.electronAPI.getSystemInfo) {
                window.electronAPI.getSystemInfo().then(info => {
                    systemInfo = info;
                    if (systemInfo) {
                        document.getElementById('osVersion').textContent = systemInfo.osVersion || '-';
                        document.getElementById('computerName').textContent = systemInfo.computerName || '-';
                        document.getElementById('ipAddress').textContent = systemInfo.ipAddress || '-';
                        document.getElementById('diskSerial').textContent = systemInfo.diskSerialNumber || '-';
                    }
                });
            }
        }

        // 显示/隐藏日志
        function showLogs() {
            const logsSection = document.getElementById('logsSection');
            if (logsSection.style.display === 'none') {
                logsSection.style.display = 'block';
                addLog('info', '打开系统日志');
            } else {
                logsSection.style.display = 'none';
            }
        }

        // 最小化窗口
        function minimizeWindow() {
            if (window.electronAPI && window.electronAPI.minimizeWindow) {
                window.electronAPI.minimizeWindow();
                isMinimized = true;
                minimizedIndicator.style.display = 'block';
                addLog('info', '窗口已最小化');
            }
        }

        // 恢复窗口
        function restoreWindow() {
            if (window.electronAPI && window.electronAPI.restoreWindow) {
                window.electronAPI.restoreWindow();
                isMinimized = false;
                minimizedIndicator.style.display = 'none';
                addLog('info', '窗口已恢复');
            }
        }

        // 最小化指示器点击事件
        minimizedIndicator.addEventListener('click', restoreWindow);

        // 初始化应用
        async function initializeApp() {
            try {
                updateStatus('正在获取系统信息...', 'connecting');
                
                // 获取系统信息
                systemInfo = await window.electronAPI.getSystemInfo();
                console.log('系统信息:', systemInfo);

                updateStatus('正在验证硬件信息...', 'connecting');
                
                // 尝试硬件验证
                const hardwareResult = await window.electronAPI.verifyHardware();
                
                if (hardwareResult.success) {
                    // 硬件验证成功，直接登录
                    studentInfo = hardwareResult.data.student;
                    await connectWebSocket(hardwareResult.data.token);
                    showSystemInfo();
                    isAuthenticated = true;
                } else {
                    // 硬件验证失败，显示登录表单
                    showLoginForm();
                }
            } catch (error) {
                console.error('初始化失败:', error);
                updateStatus('初始化失败，请重试', 'error');
                setTimeout(initializeApp, 3000);
            }
        }

        // 连接WebSocket
        async function connectWebSocket(token) {
            try {
                updateStatus('正在连接服务器...', 'connecting');
                const connected = await window.electronAPI.connectWebSocket(token);
                
                if (connected) {
                    updateStatus('连接成功', 'connected');
                    startHeartbeat();
                } else {
                    throw new Error('WebSocket连接失败');
                }
            } catch (error) {
                console.error('连接失败:', error);
                updateStatus('连接服务器失败', 'error');
                throw error;
            }
        }

        // 开始心跳
        function startHeartbeat() {
            setInterval(async () => {
                try {
                    await window.electronAPI.sendHeartbeat();
                } catch (error) {
                    console.error('心跳发送失败:', error);
                }
            }, 30000); // 30秒间隔
        }

        // 登录处理
        async function handleLogin() {
            const phone = phoneInput.value.trim();
            const password = passwordInput.value.trim();

            if (!phone || !password) {
                updateStatus('请输入手机号和密码', 'error');
                return;
            }

            try {
                loginBtn.disabled = true;
                updateStatus('正在验证身份...', 'connecting');

                const credentials = {
                    phone,
                    password,
                    diskSerialNumber: systemInfo.diskSerialNumber
                };

                const result = await window.electronAPI.authenticate(credentials);

                if (result.success) {
                    studentInfo = result.data.student;
                    await connectWebSocket(result.data.token);
                    showSystemInfo();
                    isAuthenticated = true;
                } else {
                    updateStatus(result.message || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录失败:', error);
                updateStatus('登录过程中发生错误', 'error');
            } finally {
                loginBtn.disabled = false;
            }
        }

        // 事件监听
        // 承诺书事件
        agreeCheckbox.addEventListener('change', handleAgreeCheckbox);
        agreeBtn.addEventListener('click', handleAgree);
        rejectBtn.addEventListener('click', handleReject);

        // 登录事件
        loginBtn.addEventListener('click', handleLogin);

        passwordInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });

        // 监听来自主进程的事件
        window.electronAPI.onConfigUpdate((config) => {
            console.log('收到配置更新:', config);
            addLog('info', '收到管理端配置更新');

            // 更新网络状态显示
            const networkStatusEl = document.getElementById('networkStatus');
            if (networkStatusEl) {
                const indicator = networkStatusEl.querySelector('.status-indicator');
                if (config.networkEnabled) {
                    networkStatusEl.innerHTML = '<span class="status-indicator status-online"></span>已连接';
                    addLog('info', '网络连接已启用');
                } else {
                    networkStatusEl.innerHTML = '<span class="status-indicator status-offline"></span>已断开';
                    addLog('warning', '网络连接已被管理员禁用');
                    updateStatus('网络连接已被管理员禁用', 'error');
                }
            }

            // 更新USB状态显示
            const usbStatusEl = document.getElementById('usbStatus');
            if (usbStatusEl) {
                if (config.usbEnabled) {
                    usbStatusEl.innerHTML = '<span class="status-indicator status-online"></span>已启用';
                    addLog('info', 'USB端口已启用');
                } else {
                    usbStatusEl.innerHTML = '<span class="status-indicator status-offline"></span>已禁用';
                    addLog('warning', 'USB端口已被管理员禁用');
                }
            }

            // 执行网络控制
            if (typeof config.networkEnabled !== 'undefined') {
                // 传递服务器URL以保持监控连接
                const serverURL = 'http://localhost:3000';
                window.electronAPI.setNetworkEnabled(config.networkEnabled, serverURL);
            }

            // 执行USB控制
            if (typeof config.usbEnabled !== 'undefined') {
                window.electronAPI.setUSBEnabled(config.usbEnabled);
            }

            // 更新黑名单进程监控
            if (config.blacklistedProcesses) {
                addLog('info', `更新黑名单进程: ${config.blacklistedProcesses.length} 个`);
            }
        });

        window.electronAPI.onForceDisconnect((data) => {
            console.log('收到强制断开指令:', data);
            addLog('error', '管理员强制断开连接，程序即将退出');
            updateStatus('管理员强制断开连接，程序即将退出', 'error');
        });

        window.electronAPI.onConnectionStatus((status) => {
            console.log('连接状态变化:', status);
            const connectionStatusEl = document.getElementById('connectionStatus');
            if (status.connected) {
                connectionStatusEl.innerHTML = '<span class="status-indicator status-online"></span>已连接';
                addLog('info', '与监考服务器连接成功');
            } else {
                connectionStatusEl.innerHTML = '<span class="status-indicator status-offline"></span>已断开';
                addLog('error', '与监考服务器连接断开');
                updateStatus('与服务器连接断开', 'error');
            }
        });

        // 初始化应用
        function initializeApp() {
            addLog('info', '监考客户端启动');
            addLog('info', '正在初始化系统...');

            // 显示承诺书
            showAgreementForm();

            // 获取系统信息
            if (window.electronAPI && window.electronAPI.getSystemInfo) {
                window.electronAPI.getSystemInfo().then(info => {
                    systemInfo = info;
                    addLog('info', '系统信息获取成功');
                }).catch(err => {
                    addLog('error', '系统信息获取失败: ' + err.message);
                });
            }

            addLog('info', '请先阅读并同意诚信考试承诺书');
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
