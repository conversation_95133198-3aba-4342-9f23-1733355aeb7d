"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StudentStatus = exports.StudentStatusInstance = void 0;
const sequelize_1 = require("sequelize");
class StudentStatusInstance extends sequelize_1.Model {
    // 辅助方法：获取当前进程列表
    getCurrentProcesses() {
        try {
            return this.current_processes ? JSON.parse(this.current_processes) : [];
        }
        catch {
            return [];
        }
    }
    // 辅助方法：设置当前进程列表
    setCurrentProcesses(processes) {
        this.current_processes = JSON.stringify(processes);
    }
    // 辅助方法：获取系统信息
    getSystemInfo() {
        try {
            return this.system_info ? JSON.parse(this.system_info) : {};
        }
        catch {
            return {};
        }
    }
    // 辅助方法：设置系统信息
    setSystemInfo(info) {
        this.system_info = JSON.stringify(info);
    }
}
exports.StudentStatusInstance = StudentStatusInstance;
const StudentStatus = (sequelize) => {
    StudentStatusInstance.init({
        id: {
            type: sequelize_1.DataTypes.UUID,
            defaultValue: sequelize_1.DataTypes.UUIDV4,
            primaryKey: true
        },
        student_id: {
            type: sequelize_1.DataTypes.UUID,
            allowNull: false,
            unique: true,
            references: {
                model: 'students',
                key: 'id'
            },
            comment: '考生ID'
        },
        is_online: {
            type: sequelize_1.DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否在线'
        },
        last_heartbeat: {
            type: sequelize_1.DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize_1.DataTypes.NOW,
            comment: '最后心跳时间'
        },
        current_processes: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            defaultValue: null,
            comment: '当前运行进程（JSON格式）'
        },
        system_info: {
            type: sequelize_1.DataTypes.TEXT,
            allowNull: true,
            defaultValue: null,
            comment: '系统信息（JSON格式）'
        }
    }, {
        sequelize,
        modelName: 'StudentStatus',
        tableName: 'student_status',
        indexes: [
            {
                unique: true,
                fields: ['student_id']
            },
            {
                fields: ['is_online']
            },
            {
                fields: ['last_heartbeat']
            }
        ]
    });
    return StudentStatusInstance;
};
exports.StudentStatus = StudentStatus;
//# sourceMappingURL=StudentStatus.js.map