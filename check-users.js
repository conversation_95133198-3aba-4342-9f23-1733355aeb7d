const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库路径
const dbPath = path.join(__dirname, 'server', 'database.sqlite');

console.log('🔍 检查数据库用户:', dbPath);

// 连接数据库
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ 数据库连接失败:', err.message);
        return;
    }
    console.log('✅ 数据库连接成功');
});

// 查询用户
db.all('SELECT id, name, phone, police_id, department, major FROM students', (err, rows) => {
    if (err) {
        console.error('❌ 查询失败:', err);
    } else {
        console.log(`\n📊 数据库中共有 ${rows.length} 个用户:`);
        rows.forEach((row, i) => {
            console.log(`${i+1}. ${row.name} - ${row.phone} - ${row.police_id} - ${row.department}`);
        });
    }
    
    db.close((err) => {
        if (err) {
            console.error('❌ 关闭数据库失败:', err.message);
        } else {
            console.log('\n✅ 数据库连接已关闭');
        }
    });
});
