{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/routes/config.ts"], "names": [], "mappings": ";;;;;;AAAA,qCAAiC;AACjC,8CAAsB;AACtB,sCAA8C;AAC9C,6DAAuE;AAEvE,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AA4KL,8BAAY;AA1K/B,OAAO;AACP,MAAM,YAAY,GAAG,aAAG,CAAC,MAAM,CAAC;IAC9B,oBAAoB,EAAE,aAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,aAAG,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAChE,UAAU,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACpC,cAAc,EAAE,aAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;IACxC,kBAAkB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;IAC1E,iBAAiB,EAAE,aAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE;CAC5E,CAAC,CAAC;AAEH,SAAS;AACT,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxD,IAAI,MAAM,GAAG,MAAM,0BAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,gBAAgB;QAChB,MAAM,GAAG,MAAM,0BAAiB,CAAC,MAAM,CAAC;YACtC,EAAE,EAAE,SAAS;YACb,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC;gBACpC,QAAQ;gBACR,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,YAAY;aACb,CAAC;YACF,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;YACrB,mBAAmB,EAAE,IAAI;YACzB,kBAAkB,EAAE,KAAK;SAC1B,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,oBAAoB,EAAE,MAAM,CAAC,uBAAuB,EAAE;YACtD,UAAU,EAAE,MAAM,CAAC,WAAW;YAC9B,cAAc,EAAE,MAAM,CAAC,eAAe;YACtC,kBAAkB,EAAE,MAAM,CAAC,mBAAmB;YAC9C,iBAAiB,EAAE,MAAM,CAAC,kBAAkB;YAC5C,SAAS,EAAE,MAAM,CAAC,UAAU;SAC7B;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAEJ,SAAS;AACT,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxD,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACzD,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAA,0BAAW,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,cAAc,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAAG,KAAK,CAAC;IAE1G,IAAI,MAAM,GAAG,MAAM,0BAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,QAAQ;QACR,MAAM,GAAG,MAAM,0BAAiB,CAAC,MAAM,CAAC;YACtC,EAAE,EAAE,SAAS;YACb,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;YAC3D,WAAW,EAAE,UAAU;YACvB,eAAe,EAAE,cAAc;YAC/B,mBAAmB,EAAE,kBAAkB;YACvC,kBAAkB,EAAE,iBAAiB;SACtC,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,SAAS;QACT,MAAM,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;QACrD,MAAM,CAAC,WAAW,GAAG,UAAU,CAAC;QAChC,MAAM,CAAC,eAAe,GAAG,cAAc,CAAC;QACxC,MAAM,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAChD,MAAM,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAE9C,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,oBAAoB,EAAE,MAAM,CAAC,uBAAuB,EAAE;YACtD,UAAU,EAAE,MAAM,CAAC,WAAW;YAC9B,cAAc,EAAE,MAAM,CAAC,eAAe;YACtC,kBAAkB,EAAE,MAAM,CAAC,mBAAmB;YAC9C,iBAAiB,EAAE,MAAM,CAAC,kBAAkB;YAC5C,SAAS,EAAE,MAAM,CAAC,UAAU;SAC7B;QACD,OAAO,EAAE,UAAU;KACpB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAEJ,UAAU;AACV,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IAC9D,MAAM,aAAa,GAAG;QACpB,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC;YACpC,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,aAAa;YACb,YAAY;SACb,CAAC;QACF,WAAW,EAAE,KAAK;QAClB,eAAe,EAAE,IAAI;QACrB,mBAAmB,EAAE,IAAI;QACzB,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAEF,IAAI,MAAM,GAAG,MAAM,0BAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,MAAM,0BAAiB,CAAC,MAAM,CAAC;YACtC,EAAE,EAAE,SAAS;YACb,GAAG,aAAa;SACjB,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACrC,CAAC;IAED,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,oBAAoB,EAAE,MAAM,CAAC,uBAAuB,EAAE;YACtD,UAAU,EAAE,MAAM,CAAC,WAAW;YAC9B,cAAc,EAAE,MAAM,CAAC,eAAe;YACtC,kBAAkB,EAAE,MAAM,CAAC,mBAAmB;YAC9C,iBAAiB,EAAE,MAAM,CAAC,kBAAkB;YAC5C,SAAS,EAAE,MAAM,CAAC,UAAU;SAC7B;QACD,OAAO,EAAE,WAAW;KACrB,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAEJ,UAAU;AACV,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACjE,IAAI,MAAM,GAAG,MAAM,0BAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,gBAAgB;QAChB,MAAM,GAAG,MAAM,0BAAiB,CAAC,MAAM,CAAC;YACtC,EAAE,EAAE,SAAS;YACb,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC;gBACpC,QAAQ;gBACR,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,YAAY;gBACZ,SAAS;gBACT,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,gBAAgB;aACjB,CAAC;YACF,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;YACrB,mBAAmB,EAAE,IAAI;YACzB,kBAAkB,EAAE,KAAK;SAC1B,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,oBAAoB,EAAE,MAAM,CAAC,uBAAuB,EAAE;YACtD,WAAW,EAAE,MAAM,CAAC,UAAU;SAC/B;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC"}